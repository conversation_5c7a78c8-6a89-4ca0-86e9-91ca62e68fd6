# Rate Limiting Integration Plan for Resumatter

## Overview

This plan integrates flexible, production-ready rate limiting into Resumatter's existing middleware stack, leveraging the current configuration system and maintaining the clean architecture patterns already established.

## Design Goals

1. **Per-Operation Rate Limiting**: Different limits for tailor/evaluate/analyze operations
2. **Multiple Storage Backends**: In-memory (development) and Redis (production)
3. **Flexible Rate Limiting Strategies**: Token bucket, sliding window, fixed window
4. **Client Identification**: IP-based, API key-based, or custom headers
5. **Graceful Degradation**: Configurable behavior when limits are exceeded
6. **Observability Integration**: Metrics and logging for rate limit events

## Architecture Design

### 1. Configuration Structure

Add to `pkg/config/config.go`:

```go
// Config struct - add RateLimit field
type Config struct {
    AI            AIConfig            `mapstructure:"ai"`
    Providers     ProviderConfig      `mapstructure:"providers"`
    Server        ServerConfig        `mapstructure:"server"`
    Auth          AuthConfig          `mapstructure:"auth"`
    RateLimit     RateLimitConfig     `mapstructure:"rate_limit"`  // NEW
    Logging       LoggingConfig       `mapstructure:"logging"`
    Observability ObservabilityConfig `mapstructure:"observability"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
    Enabled     bool                           `mapstructure:"enabled"`
    Backend     string                         `mapstructure:"backend"`      // "memory", "redis"
    Strategy    string                         `mapstructure:"strategy"`     // "token_bucket", "sliding_window", "fixed_window"
    KeyBy       string                         `mapstructure:"key_by"`       // "ip", "api_key", "header"
    HeaderName  string                         `mapstructure:"header_name"`  // Custom header for identification
    Operations  map[string]OperationRateLimit  `mapstructure:"operations"`
    Redis       RedisConfig                    `mapstructure:"redis"`
    Defaults    DefaultRateLimit               `mapstructure:"defaults"`
}

// OperationRateLimit holds per-operation rate limit settings
type OperationRateLimit struct {
    RequestsPerMinute int    `mapstructure:"requests_per_minute"`
    RequestsPerHour   int    `mapstructure:"requests_per_hour"`
    RequestsPerDay    int    `mapstructure:"requests_per_day"`
    BurstSize         int    `mapstructure:"burst_size"`
    Enabled           bool   `mapstructure:"enabled"`
}

// DefaultRateLimit holds default rate limit settings
type DefaultRateLimit struct {
    RequestsPerMinute int `mapstructure:"requests_per_minute"`
    RequestsPerHour   int `mapstructure:"requests_per_hour"`
    RequestsPerDay    int `mapstructure:"requests_per_day"`
    BurstSize         int `mapstructure:"burst_size"`
}

// RedisConfig holds Redis configuration for rate limiting
type RedisConfig struct {
    Address  string `mapstructure:"address"`
    Password string `mapstructure:"password"`
    DB       int    `mapstructure:"db"`
    PoolSize int    `mapstructure:"pool_size"`
}
```

### 2. Package Structure

Create new package: `pkg/ratelimit/`

```
pkg/ratelimit/
├── interface.go          # Rate limiter interface
├── config.go            # Configuration helpers
├── limiter.go           # Main rate limiter implementation
├── backends/
│   ├── memory.go        # In-memory backend
│   ├── redis.go         # Redis backend
│   └── interface.go     # Backend interface
├── strategies/
│   ├── token_bucket.go  # Token bucket algorithm
│   ├── sliding_window.go # Sliding window algorithm
│   ├── fixed_window.go  # Fixed window algorithm
│   └── interface.go     # Strategy interface
├── middleware.go        # Gin middleware
├── metrics.go          # Rate limit metrics
└── ratelimit_test.go   # Comprehensive tests
```

### 3. Core Interfaces

```go
// pkg/ratelimit/interface.go
type RateLimiter interface {
    Allow(ctx context.Context, key string, operation string) (*Result, error)
    Reset(ctx context.Context, key string, operation string) error
    GetStatus(ctx context.Context, key string, operation string) (*Status, error)
    Close() error
}

type Result struct {
    Allowed       bool
    Remaining     int64
    ResetTime     time.Time
    RetryAfter    time.Duration
    TotalRequests int64
}

type Status struct {
    Limit         int64
    Remaining     int64
    ResetTime     time.Time
    WindowStart   time.Time
    TotalRequests int64
}

// Backend interface for storage
type Backend interface {
    Get(ctx context.Context, key string) (*BackendData, error)
    Set(ctx context.Context, key string, data *BackendData, ttl time.Duration) error
    Increment(ctx context.Context, key string, window time.Duration) (int64, error)
    Close() error
}

// Strategy interface for rate limiting algorithms
type Strategy interface {
    Allow(ctx context.Context, backend Backend, key string, limit int64, window time.Duration) (*Result, error)
    GetStatus(ctx context.Context, backend Backend, key string, limit int64, window time.Duration) (*Status, error)
}
```

## Implementation Plan

### Phase 1: Core Infrastructure (2-3 days)

#### Step 1.1: Configuration Integration
- [x] Add `RateLimitConfig` to main config struct
- [x] Update `config.example.yaml` with rate limit examples
- [x] Add validation for rate limit configuration
- [x] Update Viper bindings for environment variables

#### Step 1.2: Core Package Structure
- [x] Create `pkg/ratelimit/` package
- [x] Implement core interfaces (`interface.go`)
- [x] Create configuration helpers (`config.go`)
- [x] Set up package-level tests

#### Step 1.3: Backend Implementations
- [x] Implement in-memory backend (`backends/memory.go`)
- [x] Implement Redis backend (`backends/redis.go`)
- [x] Add backend factory pattern
- [x] Unit tests for both backends

### Phase 2: Rate Limiting Strategies (2-3 days)

#### Step 2.1: Token Bucket Strategy
- [x] Implement token bucket algorithm (`strategies/token_bucket.go`)
- [x] Configurable refill rate and amount
- [x] Burst handling with bucket capacity
- [x] Comprehensive test suite
- [x] Factory pattern integration

#### Step 2.2: Sliding Window Strategy
- [x] Create sliding window stub (`strategies/sliding_window.go`)
- [x] Configuration validation and factory integration
- [x] Stub tests for interface compliance
- [ ] Full implementation (future phase)

#### Step 2.3: Fixed Window Strategy
- [x] Implement fixed window algorithm (`strategies/fixed_window.go`)
- [x] Window boundary calculation and reset logic
- [x] Comprehensive test suite (13 test cases)
- [x] Integration with factory pattern
- [x] Production-ready implementation

### Phase 3: Middleware Integration (1-2 days)

#### Step 3.1: Gin Middleware
- [x] Core middleware implementation (`middleware.go`)
- [x] Client identification (IP, API key, custom header)
- [x] Operation extraction from routes
- [x] Standard rate limit headers (X-RateLimit-*)
- [x] Comprehensive test suite (8 test cases)
- [x] Configurable middleware options
```go
// pkg/ratelimit/middleware.go
func GinMiddleware(limiter RateLimiter, config *RateLimitConfig) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Extract client identifier (IP, API key, header)
        clientKey := extractClientKey(c, config)
        
        // Determine operation from route
        operation := extractOperation(c)
        
        // Check rate limit
        result, err := limiter.Allow(c.Request.Context(), clientKey, operation)
        if err != nil {
            // Log error and allow request (fail open)
            c.Next()
            return
        }
        
        // Add rate limit headers
        addRateLimitHeaders(c, result)
        
        if !result.Allowed {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "error": "Rate limit exceeded",
                "retry_after": result.RetryAfter.Seconds(),
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

#### Step 3.2: HTTP Server Integration
Update `internal/server/http.go`:
```go
func (s *Server) setupRouter() {
    s.Router = gin.New()
    
    // Existing middleware
    s.Router.Use(otelgin.Middleware("resumatter-http"))
    s.Router.Use(s.loggingMiddleware())
    s.Router.Use(s.recoveryMiddleware())
    s.Router.Use(s.corsMiddleware())
    
    // NEW: Rate limiting middleware (before auth)
    if s.Config.RateLimit.Enabled {
        rateLimiter, err := s.createRateLimiter()
        if err != nil {
            s.logger.ErrorWithErr(context.Background(), "Failed to create rate limiter", err)
        } else {
            s.Router.Use(ratelimit.GinMiddleware(rateLimiter, &s.Config.RateLimit))
        }
    }
    
    // Auth middleware after rate limiting
    if s.Config.Auth.Enabled {
        s.Router.Use(s.apiKeyAuthMiddleware())
    }
    
    // Routes...
}
```

### Phase 4: Observability & Metrics (1 day)

#### Step 4.1: Metrics Integration
```go
// pkg/ratelimit/metrics.go
type Metrics struct {
    requestsTotal     prometheus.CounterVec
    requestsBlocked   prometheus.CounterVec
    limitExceeded     prometheus.CounterVec
    backendErrors     prometheus.CounterVec
    operationDuration prometheus.HistogramVec
}

func (m *Metrics) RecordRequest(operation, clientType string, allowed bool) {
    labels := prometheus.Labels{
        "operation":   operation,
        "client_type": clientType,
        "allowed":     fmt.Sprintf("%t", allowed),
    }
    m.requestsTotal.With(labels).Inc()
    
    if !allowed {
        m.requestsBlocked.With(labels).Inc()
    }
}
```

#### Step 4.2: Logging Integration
- Add structured logging for rate limit events
- Log rate limit violations with client identification
- Add debug logging for rate limit decisions

## Configuration Examples

### Development Configuration
```yaml
rate_limit:
  enabled: true
  backend: "memory"
  strategy: "token_bucket"
  key_by: "ip"
  
  defaults:
    requests_per_minute: 60
    requests_per_hour: 1000
    requests_per_day: 10000
    burst_size: 10
  
  operations:
    tailor:
      enabled: true
      requests_per_minute: 10    # AI operations are expensive
      requests_per_hour: 100
      burst_size: 5
    
    evaluate:
      enabled: true
      requests_per_minute: 15
      requests_per_hour: 150
      burst_size: 5
    
    analyze:
      enabled: true
      requests_per_minute: 20    # Lighter operation
      requests_per_hour: 200
      burst_size: 8
```

### Production Configuration
```yaml
rate_limit:
  enabled: true
  backend: "redis"
  strategy: "sliding_window"
  key_by: "api_key"              # More precise than IP
  
  redis:
    address: "redis:6379"
    password: "${REDIS_PASSWORD}"
    db: 1
    pool_size: 10
  
  defaults:
    requests_per_minute: 30
    requests_per_hour: 500
    requests_per_day: 5000
    burst_size: 5
  
  operations:
    tailor:
      enabled: true
      requests_per_minute: 5     # Stricter for expensive operations
      requests_per_hour: 50
      requests_per_day: 500
      burst_size: 2
    
    evaluate:
      enabled: true
      requests_per_minute: 8
      requests_per_hour: 80
      requests_per_day: 800
      burst_size: 3
    
    analyze:
      enabled: true
      requests_per_minute: 12
      requests_per_hour: 120
      requests_per_day: 1200
      burst_size: 5
```

## Testing Strategy

### Unit Tests
- [ ] Backend implementations (memory, Redis)
- [ ] Rate limiting strategies (token bucket, sliding window, fixed window)
- [ ] Configuration validation
- [ ] Middleware functionality

### Integration Tests
- [ ] End-to-end rate limiting with HTTP server
- [ ] Redis backend with real Redis instance
- [ ] Multiple concurrent clients
- [ ] Rate limit recovery scenarios

### Load Tests
- [ ] Performance impact measurement
- [ ] Rate limit accuracy under load
- [ ] Backend performance comparison

## Deployment Considerations

### Environment Variables
```bash
# Rate limiting (with RESUMATTER prefix)
RESUMATTER_RATE_LIMIT_ENABLED=true
RESUMATTER_RATE_LIMIT_BACKEND=redis
RESUMATTER_RATE_LIMIT_STRATEGY=sliding_window
RESUMATTER_RATE_LIMIT_KEY_BY=api_key
RESUMATTER_RATE_LIMIT_HEADER_NAME=X-Client-ID

# Redis configuration (standard Redis env vars)
REDIS_ADDRESS=redis:6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=1
REDIS_POOL_SIZE=10

# Per-operation limits (with RESUMATTER prefix)
RESUMATTER_RATE_LIMIT_OPERATIONS_TAILOR_REQUESTS_PER_MINUTE=5
RESUMATTER_RATE_LIMIT_OPERATIONS_EVALUATE_REQUESTS_PER_MINUTE=8
RESUMATTER_RATE_LIMIT_OPERATIONS_ANALYZE_REQUESTS_PER_MINUTE=12
```

### Docker Compose Integration
```yaml
services:
  resumatter:
    environment:
      - RESUMATTER_RATE_LIMIT_ENABLED=true
      - RESUMATTER_RATE_LIMIT_BACKEND=redis
      - RESUMATTER_RATE_LIMIT_STRATEGY=sliding_window
      - RESUMATTER_RATE_LIMIT_KEY_BY=api_key
      - REDIS_ADDRESS=redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - redis
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
```

## Monitoring & Alerting

### Key Metrics
- Rate limit violations per operation
- Backend response times
- Client identification accuracy
- Rate limit effectiveness

### Alerts
- High rate limit violation rates
- Backend connectivity issues
- Unusual traffic patterns
- Rate limit bypass attempts

## Migration Strategy

### Phase 1: Development
1. Implement with `enabled: false` by default
2. Add comprehensive tests
3. Test with development traffic

### Phase 2: Staging
1. Enable with generous limits
2. Monitor for false positives
3. Tune limits based on real usage

### Phase 3: Production
1. Gradual rollout with monitoring
2. Implement proper alerting
3. Fine-tune based on production metrics

## Progress Tracking

### Phase 1: Core Infrastructure
- [x] Configuration integration
- [x] Core package structure
- [x] Backend implementations

### Phase 2: Rate Limiting Strategies
- [x] Token bucket strategy
- [x] Sliding window strategy (stub)
- [x] Fixed window strategy

### Phase 3: Middleware Integration
- [ ] Gin middleware implementation
- [ ] HTTP server integration
- [ ] Route operation detection

### Phase 4: Observability & Metrics
- [ ] Metrics integration
- [ ] Logging integration
- [ ] Monitoring setup

### Testing & Deployment
- [ ] Unit tests
- [ ] Integration tests
- [ ] Load tests
- [ ] Documentation
- [ ] Production deployment

## Notes

- Rate limiting should be implemented before authentication middleware to prevent auth bypass attempts
- Use fail-open strategy for backend errors to maintain service availability
- Consider implementing rate limit warming for new clients
- Monitor false positive rates and adjust limits accordingly
- Implement proper cleanup for expired rate limit data

### Environment Variable Pattern

Resumatter follows a consistent environment variable pattern:

**RESUMATTER prefix (automatic)**: Most configuration uses the `RESUMATTER_` prefix via Viper's `SetEnvPrefix("RESUMATTER")` and `AutomaticEnv()`. Config path `rate_limit.enabled` automatically maps to `RESUMATTER_RATE_LIMIT_ENABLED`.

**Explicit BindEnv (exceptions)**: Only used for external/standard environment variables:
- `GEMINI_API_KEY` (Google's standard)
- `GOOGLE_CLOUD_PROJECT`, `GOOGLE_CLOUD_REGION` (GCP standards)
- `OTEL_TRACING_ENABLED`, `OTEL_SERVICE_NAME` (OpenTelemetry standards)
- `ENVIRONMENT`, `PORT` (common deployment standards)
- `REDIS_ADDRESS`, `REDIS_PASSWORD`, `REDIS_DB` (Redis standards)

**Rate limiting variables**:
- Most use RESUMATTER prefix: `RESUMATTER_RATE_LIMIT_ENABLED`, `RESUMATTER_RATE_LIMIT_BACKEND`
- Redis config uses standard Redis env vars: `REDIS_ADDRESS`, `REDIS_PASSWORD`