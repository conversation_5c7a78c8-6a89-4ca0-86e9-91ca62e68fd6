package observability

import (
	"context"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"

	"resumatter/pkg/logger"
)

// MetricsCollector provides high-level metrics collection methods
type MetricsCollector struct {
	provider *Provider
	logger   logger.Logger

	// Business metrics instruments
	resumesTailoredCounter  metric.Int64Counter
	resumesEvaluatedCounter metric.Int64Counter
	jobsAnalyzedCounter     metric.Int64Counter
	atsScoreHist            metric.Float64Histogram
	jobQualityScoreHist     metric.Float64Histogram

	// AI-specific metrics instruments
	aiRequestsCounter     metric.Int64Counter
	aiRequestDuration     metric.Float64Histogram
	aiTokensTotal         metric.Int64Counter
	aiPromptTokens        metric.Int64Counter
	aiCompletionTokens    metric.Int64Counter
	aiCircuitBreakerState metric.Float64Gauge
	aiProviderErrors      metric.Int64Counter
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(provider *Provider) (*MetricsCollector, error) {
	if provider == nil {
		return nil, nil // Return nil if no provider (metrics disabled)
	}

	collector := &MetricsCollector{
		provider: provider,
		logger:   provider.Logger().Named("metrics"),
	}

	// Initialize instruments if metrics are enabled
	if provider.config.MetricsEnabled {
		if err := collector.initInstruments(); err != nil {
			return nil, err
		}
	}

	return collector, nil
}

// initInstruments creates all the metrics instruments
func (m *MetricsCollector) initInstruments() error {
	meter := m.provider.Meter("resumatter")
	var err error

	// Business metrics
	m.resumesTailoredCounter, err = meter.Int64Counter(
		"resumatter_resumes_tailored_total",
		metric.WithDescription("Total number of resumes tailored"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.resumesEvaluatedCounter, err = meter.Int64Counter(
		"resumatter_resumes_evaluated_total",
		metric.WithDescription("Total number of resumes evaluated"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.jobsAnalyzedCounter, err = meter.Int64Counter(
		"resumatter_jobs_analyzed_total",
		metric.WithDescription("Total number of job descriptions analyzed"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.atsScoreHist, err = meter.Float64Histogram(
		"resumatter_ats_score",
		metric.WithDescription("Distribution of ATS scores"),
		metric.WithUnit("1"),
		metric.WithExplicitBucketBoundaries(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100),
	)
	if err != nil {
		return err
	}

	m.jobQualityScoreHist, err = meter.Float64Histogram(
		"resumatter_job_quality_score",
		metric.WithDescription("Distribution of job quality scores"),
		metric.WithUnit("1"),
		metric.WithExplicitBucketBoundaries(0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100),
	)
	if err != nil {
		return err
	}

	// AI-specific metrics instruments
	m.aiRequestsCounter, err = meter.Int64Counter(
		"resumatter_ai_requests_total",
		metric.WithDescription("Total number of AI requests"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.aiRequestDuration, err = meter.Float64Histogram(
		"resumatter_ai_request_duration_seconds",
		metric.WithDescription("Duration of AI requests"),
		metric.WithUnit("s"),
		metric.WithExplicitBucketBoundaries(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0),
	)
	if err != nil {
		return err
	}

	m.aiTokensTotal, err = meter.Int64Counter(
		"resumatter_ai_tokens_total",
		metric.WithDescription("Total number of AI tokens consumed"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.aiPromptTokens, err = meter.Int64Counter(
		"resumatter_ai_prompt_tokens_total",
		metric.WithDescription("Total number of AI prompt tokens"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.aiCompletionTokens, err = meter.Int64Counter(
		"resumatter_ai_completion_tokens_total",
		metric.WithDescription("Total number of AI completion tokens"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.aiCircuitBreakerState, err = meter.Float64Gauge(
		"resumatter_ai_circuit_breaker_state",
		metric.WithDescription("AI circuit breaker state (0=closed, 1=open, 2=half-open)"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.aiProviderErrors, err = meter.Int64Counter(
		"resumatter_ai_provider_errors_total",
		metric.WithDescription("Total number of AI provider errors"),
		metric.WithUnit("1"),
	)
	if err != nil {
		return err
	}

	m.logger.Info(context.Background(), "Metrics instruments initialized successfully")
	return nil
}

// Business Metrics Methods

// IncrementResumesTailored increments the counter for tailored resumes
func (m *MetricsCollector) IncrementResumesTailored(ctx context.Context) {
	if m.resumesTailoredCounter != nil {
		m.resumesTailoredCounter.Add(ctx, 1)
	}
}

// IncrementResumesEvaluated increments the counter for evaluated resumes
func (m *MetricsCollector) IncrementResumesEvaluated(ctx context.Context) {
	if m.resumesEvaluatedCounter != nil {
		m.resumesEvaluatedCounter.Add(ctx, 1)
	}
}

// IncrementJobsAnalyzed increments the counter for analyzed jobs
func (m *MetricsCollector) IncrementJobsAnalyzed(ctx context.Context) {
	if m.jobsAnalyzedCounter != nil {
		m.jobsAnalyzedCounter.Add(ctx, 1)
	}
}

// RecordATSScore records an ATS score in the histogram
func (m *MetricsCollector) RecordATSScore(ctx context.Context, score int) {
	if m.atsScoreHist != nil {
		m.atsScoreHist.Record(ctx, float64(score))
	}
}

// RecordJobQualityScore records a job quality score in the histogram
func (m *MetricsCollector) RecordJobQualityScore(ctx context.Context, score int) {
	if m.jobQualityScoreHist != nil {
		m.jobQualityScoreHist.Record(ctx, float64(score))
	}
}

// AI Metrics Methods

// RecordAIRequest records an AI request with timing and outcome
func (m *MetricsCollector) RecordAIRequest(ctx context.Context, operation, provider, model string, duration float64, success bool) {
	if m.aiRequestsCounter != nil {
		m.aiRequestsCounter.Add(ctx, 1,
			metric.WithAttributes(
				attribute.String("operation", operation),
				attribute.String("provider", provider),
				attribute.String("model", model),
				attribute.Bool("success", success),
			),
		)
	}

	if m.aiRequestDuration != nil {
		m.aiRequestDuration.Record(ctx, duration,
			metric.WithAttributes(
				attribute.String("operation", operation),
				attribute.String("provider", provider),
				attribute.String("model", model),
			),
		)
	}
}

// RecordTokenUsage records AI token consumption
func (m *MetricsCollector) RecordTokenUsage(ctx context.Context, operation, provider, model string, promptTokens, completionTokens, totalTokens int) {
	labels := metric.WithAttributes(
		attribute.String("operation", operation),
		attribute.String("provider", provider),
		attribute.String("model", model),
	)

	if m.aiPromptTokens != nil {
		m.aiPromptTokens.Add(ctx, int64(promptTokens), labels)
	}
	if m.aiCompletionTokens != nil {
		m.aiCompletionTokens.Add(ctx, int64(completionTokens), labels)
	}
	if m.aiTokensTotal != nil {
		m.aiTokensTotal.Add(ctx, int64(totalTokens), labels)
	}
}

// RecordCircuitBreakerState records circuit breaker state changes
func (m *MetricsCollector) RecordCircuitBreakerState(ctx context.Context, operation string, state float64) {
	if m.aiCircuitBreakerState != nil {
		m.aiCircuitBreakerState.Record(ctx, state,
			metric.WithAttributes(
				attribute.String("operation", operation),
			),
		)
	}
}

// RecordAIProviderError records AI provider errors
func (m *MetricsCollector) RecordAIProviderError(ctx context.Context, provider, errorType string) {
	if m.aiProviderErrors != nil {
		m.aiProviderErrors.Add(ctx, 1,
			metric.WithAttributes(
				attribute.String("provider", provider),
				attribute.String("error_type", errorType),
			),
		)
	}
}
