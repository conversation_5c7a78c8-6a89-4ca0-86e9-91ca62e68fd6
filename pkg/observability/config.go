package observability

import (
	"net/url"
	"os"
	"strconv"
	"strings"

	"resumatter/pkg/logger"
)

// LoadConfigFromEnv loads observability configuration from environment variables
func LoadConfigFromEnv() *Config {
	config := DefaultConfig()

	// Service identification
	if serviceName := os.Getenv("OTEL_SERVICE_NAME"); serviceName != "" {
		config.ServiceName = serviceName
	}
	if serviceVersion := os.Getenv("OTEL_SERVICE_VERSION"); serviceVersion != "" {
		config.ServiceVersion = serviceVersion
	}
	if environment := os.Getenv("OTEL_ENVIRONMENT"); environment != "" {
		config.Environment = environment
	}

	// Tracing configuration
	if tracingEnabled := os.Getenv("OTEL_TRACING_ENABLED"); tracingEnabled != "" {
		if enabled, err := strconv.ParseBool(tracingEnabled); err == nil {
			config.TracingEnabled = enabled
		}
	}

	if traceExporter := os.Getenv("OTEL_TRACE_EXPORTER"); traceExporter != "" {
		config.TraceExporter = parseTraceExporter(traceExporter)
	}

	// Metrics configuration
	if metricsEnabled := os.Getenv("OTEL_METRICS_ENABLED"); metricsEnabled != "" {
		if enabled, err := strconv.ParseBool(metricsEnabled); err == nil {
			config.MetricsEnabled = enabled
		}
	}

	if metricsExporter := os.Getenv("OTEL_METRICS_EXPORTER"); metricsExporter != "" {
		config.MetricsExporter = parseMetricsExporter(metricsExporter)
	}

	if metricsFilePath := os.Getenv("OTEL_METRICS_FILE_PATH"); metricsFilePath != "" {
		config.MetricsFilePath = metricsFilePath
	}

	// OTLP configuration
	if otlpEndpoint := os.Getenv("OTEL_EXPORTER_OTLP_ENDPOINT"); otlpEndpoint != "" {
		// Parse full URL to extract endpoint and path
		endpoint, urlPath := parseOTLPEndpoint(otlpEndpoint)
		config.OTLPEndpoint = endpoint
		if urlPath != "" {
			config.OTLPURLPath = urlPath
		}
	}

	if otlpURLPath := os.Getenv("OTEL_EXPORTER_OTLP_URL_PATH"); otlpURLPath != "" {
		config.OTLPURLPath = otlpURLPath
	}

	if otlpHeaders := os.Getenv("OTEL_EXPORTER_OTLP_HEADERS"); otlpHeaders != "" {
		config.OTLPHeaders = parseOTLPHeaders(otlpHeaders)
	}

	if otlpInsecure := os.Getenv("OTEL_EXPORTER_OTLP_INSECURE"); otlpInsecure != "" {
		if insecure, err := strconv.ParseBool(otlpInsecure); err == nil {
			config.OTLPInsecure = insecure
		}
	}

	return config
}

// parseTraceExporter parses the trace exporter from environment variable
func parseTraceExporter(exporter string) TraceExporter {
	switch strings.ToLower(exporter) {
	case "stdout":
		return TraceExporterStdout
	case "otlp":
		return TraceExporterOTLP
	case "none", "":
		return TraceExporterNone
	default:
		// Default to none for unknown exporters
		return TraceExporterNone
	}
}

// parseMetricsExporter parses the metrics exporter from environment variable
func parseMetricsExporter(exporter string) MetricsExporter {
	switch strings.ToLower(exporter) {
	case "stdout":
		return MetricsExporterStdout
	case "jsonfile", "json":
		return MetricsExporterJSONFile
	case "otlp":
		return MetricsExporterOTLP
	case "none", "":
		return MetricsExporterNone
	default:
		// Default to none for unknown exporters
		return MetricsExporterNone
	}
}

// parseOTLPHeaders parses OTLP headers from environment variable format
// Expected format: "key1=value1,key2=value2"
func parseOTLPHeaders(headers string) map[string]string {
	result := make(map[string]string)
	if headers == "" {
		return result
	}

	pairs := strings.SplitSeq(headers, ",")
	for pair := range pairs {
		kv := strings.SplitN(strings.TrimSpace(pair), "=", 2)
		if len(kv) == 2 {
			key := strings.TrimSpace(kv[0])
			value := strings.TrimSpace(kv[1])
			if key != "" {
				result[key] = value
			}
		}
	}
	return result
}

// parseOTLPEndpoint parses a full OTLP URL and returns the endpoint (host:port) and URL path
// Input: "https://api.honeycomb.io:443/otlp/v1/traces"
// Output: "api.honeycomb.io:443", "/otlp/v1/traces"
func parseOTLPEndpoint(fullURL string) (endpoint, urlPath string) {
	// Parse the URL
	u, err := url.Parse(fullURL)
	if err != nil {
		// If parsing fails, return the original as endpoint
		return fullURL, ""
	}

	// Extract host and port
	endpoint = u.Host
	if endpoint == "" {
		// If no host, return original
		return fullURL, ""
	}

	// Add default port if not specified
	if !strings.Contains(endpoint, ":") {
		switch u.Scheme {
		case "https":
			endpoint += ":443"
		case "http":
			endpoint += ":80"
		default:
			endpoint += ":4318" // Default OTLP port
		}
	}

	// Extract path
	urlPath = u.Path
	if urlPath == "" || urlPath == "/" {
		urlPath = ""
	}

	return endpoint, urlPath
}

// ConfigOption defines a function that configures observability Config
type ConfigOption func(*Config)

// WithServiceInfo sets service identification information
func WithServiceInfo(name, version, environment string) ConfigOption {
	return func(c *Config) {
		c.ServiceName = name
		c.ServiceVersion = version
		c.Environment = environment
	}
}

// WithTracing enables tracing with the specified exporter
func WithTracing(exporter TraceExporter) ConfigOption {
	return func(c *Config) {
		c.TracingEnabled = true
		c.TraceExporter = exporter
	}
}

// WithMetrics enables metrics with the specified exporter
func WithMetrics(exporter MetricsExporter) ConfigOption {
	return func(c *Config) {
		c.MetricsEnabled = true
		c.MetricsExporter = exporter
	}
}

// WithMetricsFile enables JSON file metrics with custom file path
func WithMetricsFile(filePath string) ConfigOption {
	return func(c *Config) {
		c.MetricsEnabled = true
		c.MetricsExporter = MetricsExporterJSONFile
		c.MetricsFilePath = filePath
	}
}

// WithLogger sets a custom logger
func WithLogger(customLogger logger.Logger) ConfigOption {
	return func(c *Config) {
		c.Logger = customLogger
	}
}

// WithOTLP enables OTLP export with the specified endpoint
func WithOTLP(endpoint string, headers map[string]string, insecure bool) ConfigOption {
	return func(c *Config) {
		c.TracingEnabled = true
		c.TraceExporter = TraceExporterOTLP
		c.MetricsEnabled = true
		c.MetricsExporter = MetricsExporterOTLP
		c.OTLPEndpoint = endpoint
		c.OTLPHeaders = headers
		c.OTLPInsecure = insecure
	}
}

// WithOTLPAdvanced enables OTLP export with advanced configuration including custom URL path
func WithOTLPAdvanced(endpoint, urlPath string, headers map[string]string, insecure bool) ConfigOption {
	return func(c *Config) {
		c.TracingEnabled = true
		c.TraceExporter = TraceExporterOTLP
		c.MetricsEnabled = true
		c.MetricsExporter = MetricsExporterOTLP
		c.OTLPEndpoint = endpoint
		c.OTLPURLPath = urlPath
		c.OTLPHeaders = headers
		c.OTLPInsecure = insecure
	}
}

// WithOTLPTracing enables OTLP tracing only
func WithOTLPTracing(endpoint string, headers map[string]string, insecure bool) ConfigOption {
	return func(c *Config) {
		c.TracingEnabled = true
		c.TraceExporter = TraceExporterOTLP
		c.OTLPEndpoint = endpoint
		c.OTLPHeaders = headers
		c.OTLPInsecure = insecure
	}
}

// WithOTLPTracingAdvanced enables OTLP tracing only with custom URL path
func WithOTLPTracingAdvanced(endpoint, urlPath string, headers map[string]string, insecure bool) ConfigOption {
	return func(c *Config) {
		c.TracingEnabled = true
		c.TraceExporter = TraceExporterOTLP
		c.OTLPEndpoint = endpoint
		c.OTLPURLPath = urlPath
		c.OTLPHeaders = headers
		c.OTLPInsecure = insecure
	}
}

// WithOTLPMetrics enables OTLP metrics only
func WithOTLPMetrics(endpoint string, headers map[string]string, insecure bool) ConfigOption {
	return func(c *Config) {
		c.MetricsEnabled = true
		c.MetricsExporter = MetricsExporterOTLP
		c.OTLPEndpoint = endpoint
		c.OTLPHeaders = headers
		c.OTLPInsecure = insecure
	}
}

// NewConfig creates a new observability configuration with options
func NewConfig(opts ...ConfigOption) *Config {
	config := DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}
	return config
}
