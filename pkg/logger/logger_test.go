//go:build fast

package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strings"
	"testing"

	"go.opentelemetry.io/otel"
	otelstdout "go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	otelresource "go.opentelemetry.io/otel/sdk/resource"
	oteltrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
)

func TestLogger_Levels(t *testing.T) {
	tests := []struct {
		name      string
		logLevel  Level
		callLevel Level
		shouldLog bool
	}{
		{"debug logs at debug level", LevelDebug, LevelDebug, true},
		{"info logs at debug level", LevelDebug, LevelInfo, true},
		{"warn logs at debug level", LevelDebug, LevelWarn, true},
		{"error logs at debug level", LevelDebug, LevelError, true},
		{"debug doesn't log at info level", LevelInfo, LevelDebug, false},
		{"info logs at info level", LevelInfo, LevelInfo, true},
		{"warn logs at info level", LevelInfo, LevelWarn, true},
		{"error logs at info level", LevelInfo, LevelError, true},
		{"debug doesn't log at error level", LevelError, LevelDebug, false},
		{"info doesn't log at error level", LevelError, LevelInfo, false},
		{"warn doesn't log at error level", LevelError, LevelWarn, false},
		{"error logs at error level", LevelError, LevelError, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			config := &Config{
				Level:  tt.logLevel,
				Format: FormatText,
				Output: &buf,
			}
			logger := New(config)
			ctx := context.Background()

			switch tt.callLevel {
			case LevelDebug:
				logger.Debug(ctx, "test message")
			case LevelInfo:
				logger.Info(ctx, "test message")
			case LevelWarn:
				logger.Warn(ctx, "test message")
			case LevelError:
				logger.Error(ctx, "test message")
			}

			output := buf.String()
			if tt.shouldLog && output == "" {
				t.Errorf("Expected log output but got none")
			}
			if !tt.shouldLog && output != "" {
				t.Errorf("Expected no log output but got: %s", output)
			}
		})
	}
}

func TestLogger_JSONFormat(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config)
	ctx := context.Background()

	logger.Info(ctx, "test message", String("key", "value"), Int("number", 42))

	var entry LogEntry
	if err := json.Unmarshal(buf.Bytes(), &entry); err != nil {
		t.Fatalf("Failed to parse JSON output: %v", err)
	}

	if entry.Level != "INFO" {
		t.Errorf("Expected level INFO, got %s", entry.Level)
	}
	if entry.Message != "test message" {
		t.Errorf("Expected message 'test message', got %s", entry.Message)
	}
	if entry.Fields["key"] != "value" {
		t.Errorf("Expected field key=value, got %v", entry.Fields["key"])
	}
	if entry.Fields["number"] != float64(42) { // JSON unmarshals numbers as float64
		t.Errorf("Expected field number=42, got %v", entry.Fields["number"])
	}
}

func TestLogger_TextFormat(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatText,
		Output: &buf,
	}
	logger := New(config)
	ctx := context.Background()

	logger.Info(ctx, "test message", String("key", "value"))

	output := buf.String()
	if !strings.Contains(output, "INFO") {
		t.Errorf("Expected INFO in output, got: %s", output)
	}
	if !strings.Contains(output, "test message") {
		t.Errorf("Expected 'test message' in output, got: %s", output)
	}
	if !strings.Contains(output, "key=value") {
		t.Errorf("Expected 'key=value' in output, got: %s", output)
	}
}

func TestLogger_WithFields(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config).With(String("component", "test"))
	ctx := context.Background()

	logger.Info(ctx, "test message", String("extra", "field"))

	var entry LogEntry
	if err := json.Unmarshal(buf.Bytes(), &entry); err != nil {
		t.Fatalf("Failed to parse JSON output: %v", err)
	}

	if entry.Fields["component"] != "test" {
		t.Errorf("Expected persistent field component=test, got %v", entry.Fields["component"])
	}
	if entry.Fields["extra"] != "field" {
		t.Errorf("Expected call field extra=field, got %v", entry.Fields["extra"])
	}
}

func TestLogger_Named(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config).Named("service").Named("component")
	ctx := context.Background()

	logger.Info(ctx, "test message")

	var entry LogEntry
	if err := json.Unmarshal(buf.Bytes(), &entry); err != nil {
		t.Fatalf("Failed to parse JSON output: %v", err)
	}

	if entry.Logger != "service.component" {
		t.Errorf("Expected logger name 'service.component', got %s", entry.Logger)
	}
}

func TestLogger_ErrorWithErr(t *testing.T) {
	var buf bytes.Buffer
	config := &Config{
		Level:  LevelError,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config)
	ctx := context.Background()

	testErr := errors.New("test error")
	logger.ErrorWithErr(ctx, "operation failed", testErr)

	var entry LogEntry
	if err := json.Unmarshal(buf.Bytes(), &entry); err != nil {
		t.Fatalf("Failed to parse JSON output: %v", err)
	}

	if entry.Level != "ERROR" {
		t.Errorf("Expected level ERROR, got %s", entry.Level)
	}
	if entry.Message != "operation failed" {
		t.Errorf("Expected message 'operation failed', got %s", entry.Message)
	}
	if entry.Fields["error"] == nil {
		t.Errorf("Expected error field to be present")
	}
}

func TestLogger_TraceIntegration(t *testing.T) {
	// --- OTel SDK setup for test ---
	exp, err := otelstdout.New(otelstdout.WithPrettyPrint())
	if err != nil {
		t.Fatalf("failed to initialize stdouttrace exporter: %v", err)
	}
	res, err := otelresource.Merge(
		otelresource.Default(),
		otelresource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName("logger-test"),
		),
	)
	if err != nil {
		t.Fatalf("failed to create resource: %v", err)
	}
	tp := oteltrace.NewTracerProvider(
		oteltrace.WithBatcher(exp),
		oteltrace.WithResource(res),
	)
	defer func() { _ = tp.Shutdown(context.Background()) }()
	otel.SetTracerProvider(tp)
	// --- end OTel SDK setup ---

	var buf bytes.Buffer
	config := &Config{
		Level:  LevelInfo,
		Format: FormatJSON,
		Output: &buf,
	}
	logger := New(config)

	// Create a tracer and span
	tracer := otel.Tracer("test")
	ctx, span := tracer.Start(context.Background(), "test-operation")
	defer span.End()

	logger.Info(ctx, "test message")

	var entry LogEntry
	if err := json.Unmarshal(buf.Bytes(), &entry); err != nil {
		t.Fatalf("Failed to parse JSON output: %v", err)
	}

	// Check that trace and span IDs are present
	if entry.TraceID == "" {
		t.Errorf("Expected trace ID to be present")
	}
	if entry.SpanID == "" {
		t.Errorf("Expected span ID to be present")
	}

	// Verify the IDs match the span context
	spanCtx := span.SpanContext()
	if entry.TraceID != spanCtx.TraceID().String() {
		t.Errorf("Expected trace ID %s, got %s", spanCtx.TraceID().String(), entry.TraceID)
	}
	if entry.SpanID != spanCtx.SpanID().String() {
		t.Errorf("Expected span ID %s, got %s", spanCtx.SpanID().String(), entry.SpanID)
	}
}

func TestFromContext(t *testing.T) {
	logger := NewDefault()
	ctx := WithLogger(context.Background(), logger)

	retrieved := FromContext(ctx)
	if retrieved == nil {
		t.Errorf("Expected to retrieve logger from context")
	}

	// Test with context without logger
	emptyCtx := context.Background()
	defaultLogger := FromContext(emptyCtx)
	if defaultLogger == nil {
		t.Errorf("Expected default logger when none in context")
	}
}

func TestFromContextOrNil(t *testing.T) {
	logger := NewDefault()
	ctx := WithLogger(context.Background(), logger)

	retrieved := FromContextOrNil(ctx)
	if retrieved == nil {
		t.Errorf("Expected to retrieve logger from context")
	}

	// Test with context without logger
	emptyCtx := context.Background()
	nilLogger := FromContextOrNil(emptyCtx)
	if nilLogger != nil {
		t.Errorf("Expected nil when no logger in context")
	}
}
