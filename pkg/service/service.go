package service

import (
	"context"
	"fmt"

	"resumatter/pkg/ai"
	"resumatter/pkg/ai/providers"
	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/logger"
	"resumatter/pkg/observability"
	"resumatter/pkg/types"
)

// Service provides the core business logic for resume operations
type Service struct {
	aiClient              ai.ClientInterface
	formatter             *formatter.Formatter
	logger                logger.Logger
	observabilityProvider observability.ProviderInterface
	middleware            observability.MiddlewareInterface
	metricsCollector      *observability.MetricsCollector
}

// Make sure Service implements ServiceInterface
var _ ServiceInterface = (*Service)(nil)

// New creates a new service instance using the option pattern
func New(opts ...ServiceOption) (*Service, error) {
	service := &Service{}

	// Apply all options
	for _, opt := range opts {
		if err := opt(service); err != nil {
			return nil, fmt.Errorf("failed to apply service option: %w", err)
		}
	}

	// Validate that required components are set
	if service.aiClient == nil {
		return nil, fmt.Errorf("AI client is required but not provided")
	}
	if service.logger == nil {
		return nil, fmt.Errorf("logger is required but not provided")
	}
	if service.formatter == nil {
		return nil, fmt.Errorf("formatter is required but not provided")
	}

	// Set up observability middleware if provider is available
	if service.observabilityProvider != nil {
		// Type assert to concrete type for middleware creation
		if provider, ok := service.observabilityProvider.(*observability.Provider); ok {
			service.middleware = observability.NewServiceMiddleware(provider)

			// Set up metrics collector
			metricsCollector, err := observability.NewMetricsCollector(provider)
			if err != nil {
				return nil, fmt.Errorf("failed to create metrics collector: %w", err)
			}
			service.metricsCollector = metricsCollector

			// Wire up metrics collector to AI client
			if aiClient, ok := service.aiClient.(*ai.Client); ok {
				aiClient.SetMetricsCollector(service.metricsCollector)
			}
		}
	}

	return service, nil
}

// NewWithConfig creates a new service instance with config-based defaults
// This is a convenience function for backward compatibility
func NewWithConfig(cfg *config.Config) (*Service, error) {
	return New(WithConfig(cfg))
}

// NewWithConfigAndLogger creates a new service instance with config and custom logger
// This is a convenience function for backward compatibility
func NewWithConfigAndLogger(cfg *config.Config, customLogger logger.Logger) (*Service, error) {
	return New(WithConfig(cfg), WithLogger(customLogger))
}

// TailorResume tailors a resume for a specific job description
func (s *Service) TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error) {
	// Add logger to context for downstream components
	ctx = logger.WithLogger(ctx, s.logger)

	// Use observability middleware if available, otherwise execute directly
	if s.middleware != nil {
		result, err := s.middleware.WrapOperationWithResult(ctx, "tailor-resume", func(ctx context.Context) (any, error) {
			return s.tailorResumeInternal(ctx, opts...)
		})
		if err != nil {
			return nil, err
		}
		return result.(*types.TailorResumeOutput), nil
	}

	return s.tailorResumeInternal(ctx, opts...)
}

// tailorResumeInternal contains the core business logic for resume tailoring
func (s *Service) tailorResumeInternal(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error) {
	s.logger.Info(ctx, "Starting resume tailoring")

	input := &types.TailorResumeInput{}
	for _, opt := range opts {
		if err := opt(input); err != nil {
			s.logger.ErrorWithErr(ctx, "Failed to apply tailor resume option", err)
			return nil, err
		}
	}

	if input.BaseResume == "" || input.JobDescription == "" {
		s.logger.Error(ctx, "Validation failed: missing required input data",
			logger.String("base_resume_empty", fmt.Sprintf("%t", input.BaseResume == "")),
			logger.String("job_description_empty", fmt.Sprintf("%t", input.JobDescription == "")))
		return nil, fmt.Errorf("both resume and job description must be provided")
	}

	s.logger.Debug(ctx, "Input validation successful",
		logger.Int("resume_length", len(input.BaseResume)),
		logger.Int("job_description_length", len(input.JobDescription)))

	// Add span attributes if middleware is available
	if s.middleware != nil {
		s.middleware.AddSpanAttributes(ctx, map[string]any{
			"resume_length":          len(input.BaseResume),
			"job_description_length": len(input.JobDescription),
		})
	}

	result, err := s.aiClient.TailorResume(ctx, *input)
	if err != nil {
		s.logger.ErrorWithErr(ctx, "AI client failed to tailor resume", err)
		return nil, fmt.Errorf("failed to tailor resume: %w", err)
	}

	s.logger.Info(ctx, "Resume tailoring completed successfully",
		logger.Int("tailored_resume_length", len(result.TailoredResume)))

	// Add result attributes if middleware is available
	if s.middleware != nil {
		s.middleware.AddSpanAttributes(ctx, map[string]any{
			"tailored_resume_length": len(result.TailoredResume),
			"ats_score":              result.ATSAnalysis.Score,
		})
	}

	// Record metrics if collector is available
	if s.metricsCollector != nil {
		s.metricsCollector.IncrementResumesTailored(ctx)
		s.metricsCollector.RecordATSScore(ctx, result.ATSAnalysis.Score)
	}

	return result, nil
}

// EvaluateResume evaluates a tailored resume against the original
func (s *Service) EvaluateResume(ctx context.Context, opts ...EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
	// Add logger to context for downstream components
	ctx = logger.WithLogger(ctx, s.logger)

	// Use observability middleware if available, otherwise execute directly
	if s.middleware != nil {
		result, err := s.middleware.WrapOperationWithResult(ctx, "evaluate-resume", func(ctx context.Context) (any, error) {
			return s.evaluateResumeInternal(ctx, opts...)
		})
		if err != nil {
			return nil, err
		}
		return result.(*types.EvaluateResumeOutput), nil
	}

	return s.evaluateResumeInternal(ctx, opts...)
}

// evaluateResumeInternal contains the core business logic for resume evaluation
func (s *Service) evaluateResumeInternal(ctx context.Context, opts ...EvaluateResumeOption) (*types.EvaluateResumeOutput, error) {
	s.logger.Info(ctx, "Starting resume evaluation")

	input := &types.EvaluateResumeInput{}
	for _, opt := range opts {
		if err := opt(input); err != nil {
			s.logger.ErrorWithErr(ctx, "Failed to apply evaluate resume option", err)
			return nil, err
		}
	}

	if input.BaseResume == "" || input.TailoredResume == "" {
		s.logger.Error(ctx, "Validation failed: missing required input data",
			logger.String("base_resume_empty", fmt.Sprintf("%t", input.BaseResume == "")),
			logger.String("tailored_resume_empty", fmt.Sprintf("%t", input.TailoredResume == "")))
		return nil, fmt.Errorf("both base resume and tailored resume must be provided")
	}

	s.logger.Debug(ctx, "Input validation successful",
		logger.Int("base_resume_length", len(input.BaseResume)),
		logger.Int("tailored_resume_length", len(input.TailoredResume)))

	// Add span attributes if middleware is available
	if s.middleware != nil {
		s.middleware.AddSpanAttributes(ctx, map[string]any{
			"base_resume_length":     len(input.BaseResume),
			"tailored_resume_length": len(input.TailoredResume),
		})
	}

	result, err := s.aiClient.EvaluateResume(ctx, *input)
	if err != nil {
		s.logger.ErrorWithErr(ctx, "AI client failed to evaluate resume", err)
		return nil, fmt.Errorf("failed to evaluate resume: %w", err)
	}

	s.logger.Info(ctx, "Resume evaluation completed successfully")

	// Add result attributes if middleware is available
	if s.middleware != nil {
		s.middleware.AddSpanAttributes(ctx, map[string]any{
			"findings_count": len(result.Findings),
		})
	}

	// Record metrics if collector is available
	if s.metricsCollector != nil {
		s.metricsCollector.IncrementResumesEvaluated(ctx)
	}

	return result, nil
}

// AnalyzeJob analyzes a job description for quality and effectiveness
func (s *Service) AnalyzeJob(ctx context.Context, opts ...AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
	// Add logger to context for downstream components
	ctx = logger.WithLogger(ctx, s.logger)

	// Use observability middleware if available, otherwise execute directly
	if s.middleware != nil {
		result, err := s.middleware.WrapOperationWithResult(ctx, "analyze-job", func(ctx context.Context) (any, error) {
			return s.analyzeJobInternal(ctx, opts...)
		})
		if err != nil {
			return nil, err
		}
		return result.(*types.AnalyzeJobOutput), nil
	}

	return s.analyzeJobInternal(ctx, opts...)
}

// analyzeJobInternal contains the core business logic for job analysis
func (s *Service) analyzeJobInternal(ctx context.Context, opts ...AnalyzeJobOption) (*types.AnalyzeJobOutput, error) {
	s.logger.Info(ctx, "Starting job description analysis")

	input := &types.AnalyzeJobInput{}
	for _, opt := range opts {
		if err := opt(input); err != nil {
			s.logger.ErrorWithErr(ctx, "Failed to apply analyze job option", err)
			return nil, err
		}
	}

	if input.JobDescription == "" {
		s.logger.Error(ctx, "Validation failed: missing required input data",
			logger.String("job_description_empty", "true"))
		return nil, fmt.Errorf("job description must be provided")
	}

	s.logger.Debug(ctx, "Input validation successful",
		logger.Int("job_description_length", len(input.JobDescription)))

	// Add span attributes if middleware is available
	if s.middleware != nil {
		s.middleware.AddSpanAttributes(ctx, map[string]any{
			"job_description_length": len(input.JobDescription),
		})
	}

	result, err := s.aiClient.AnalyzeJob(ctx, *input)
	if err != nil {
		s.logger.ErrorWithErr(ctx, "AI client failed to analyze job description", err)
		return nil, fmt.Errorf("failed to analyze job description: %w", err)
	}

	s.logger.Info(ctx, "Job description analysis completed successfully")

	// Add result attributes if middleware is available
	if s.middleware != nil {
		s.middleware.AddSpanAttributes(ctx, map[string]any{
			"job_quality_score": result.JobQualityScore,
			"clarity_score":     result.Clarity.Score,
			"inclusivity_score": result.Inclusivity.Score,
		})
	}

	// Record metrics if collector is available
	if s.metricsCollector != nil {
		s.metricsCollector.IncrementJobsAnalyzed(ctx)
		s.metricsCollector.RecordJobQualityScore(ctx, result.JobQualityScore)
	}

	return result, nil
}

// GetModelInfo retrieves model information for a specific operation
func (s *Service) GetModelInfo(ctx context.Context, operation string) (*providers.ModelInfo, error) {
	// Add logger to context for downstream components
	ctx = logger.WithLogger(ctx, s.logger)

	s.logger.Debug(ctx, "Getting model info for operation", logger.String("operation", operation))

	modelInfo, err := s.aiClient.GetModelInfo(ctx, operation)
	if err != nil {
		s.logger.ErrorWithErr(ctx, "Failed to get model info", err,
			logger.String("operation", operation))
		return nil, fmt.Errorf("failed to get model info for operation %s: %w", operation, err)
	}

	s.logger.Debug(ctx, "Successfully retrieved model info",
		logger.String("operation", operation),
		logger.String("model_name", modelInfo.Name),
		logger.String("provider", modelInfo.Provider))

	return modelInfo, nil
}

// GetOperationModels retrieves model information for all configured operations
func (s *Service) GetOperationModels(ctx context.Context) (map[string]*providers.ModelInfo, error) {
	// Add logger to context for downstream components
	ctx = logger.WithLogger(ctx, s.logger)

	s.logger.Debug(ctx, "Getting model info for all operations")

	models, err := s.aiClient.GetOperationModels(ctx)
	if err != nil {
		s.logger.ErrorWithErr(ctx, "Failed to get operation models", err)
		return nil, fmt.Errorf("failed to get operation models: %w", err)
	}

	s.logger.Info(ctx, "Successfully retrieved operation models",
		logger.Int("operation_count", len(models)))

	return models, nil
}

// Logger returns the service's logger instance
func (s *Service) Logger() logger.Logger {
	return s.logger
}

// Close closes the service and its dependencies
func (s *Service) Close() error {
	var errs []error

	// Shutdown observability provider first
	if s.observabilityProvider != nil {
		if err := s.observabilityProvider.Shutdown(context.Background()); err != nil {
			errs = append(errs, fmt.Errorf("failed to shutdown observability provider: %w", err))
		}
		// Set to nil to prevent double shutdown
		s.observabilityProvider = nil
	}

	// Close AI client
	if s.aiClient != nil {
		if err := s.aiClient.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close AI client: %w", err))
		}
	}

	// Return first error if any occurred
	if len(errs) > 0 {
		return errs[0]
	}
	return nil
}
