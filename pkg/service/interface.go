package service

import (
	"context"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/logger"
	"resumatter/pkg/types"
)

// Option types for service methods
type TailorResumeOption func(*types.TailorResumeInput) error
type EvaluateResumeOption func(*types.EvaluateResumeInput) error
type AnalyzeJobOption func(*types.AnalyzeJobInput) error

// ServiceInterface defines the contract for the resume service
type ServiceInterface interface {
	TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error)
	EvaluateResume(ctx context.Context, opts ...EvaluateResumeOption) (*types.EvaluateResumeOutput, error)
	AnalyzeJob(ctx context.Context, opts ...AnalyzeJobOption) (*types.AnalyzeJobOutput, error)
	GetModelInfo(ctx context.Context, operation string) (*providers.ModelInfo, error)
	GetOperationModels(ctx context.Context) (map[string]*providers.ModelInfo, error)
	Logger() logger.Logger
	Close() error
}
