package service

import (
	"fmt"
	"os"

	"resumatter/pkg/ai"
	"resumatter/pkg/config"
	"resumatter/pkg/formatter"
	"resumatter/pkg/logger"
	"resumatter/pkg/observability"
)

// ServiceOption defines a function that configures a Service
type ServiceOption func(*Service) error

// WithAIClient sets a custom AI client for the service
func WithAIClient(client ai.ClientInterface) ServiceOption {
	return func(s *Service) error {
		if client == nil {
			return fmt.Errorf("AI client cannot be nil")
		}
		s.aiClient = client
		return nil
	}
}

// WithLogger sets a custom logger for the service
func WithLogger(customLogger logger.Logger) ServiceOption {
	return func(s *Service) error {
		if customLogger == nil {
			return fmt.Errorf("logger cannot be nil")
		}
		s.logger = customLogger.Named("service")
		return nil
	}
}

// WithFormatter sets a custom formatter for the service
func WithFormatter(customFormatter *formatter.Formatter) ServiceOption {
	return func(s *Service) error {
		if customFormatter == nil {
			return fmt.Erro<PERSON>("formatter cannot be nil")
		}
		s.formatter = customFormatter
		return nil
	}
}

// WithObservability sets an observability provider for the service
func WithObservability(provider observability.ProviderInterface) ServiceOption {
	return func(s *Service) error {
		if provider == nil {
			return fmt.Errorf("observability provider cannot be nil")
		}
		s.observabilityProvider = provider
		return nil
	}
}

// WithConfig creates a service with default components based on config
func WithConfig(cfg *config.Config) ServiceOption {
	return func(s *Service) error {
		if cfg == nil {
			return fmt.Errorf("config cannot be nil")
		}

		if err := cfg.Validate(); err != nil {
			return fmt.Errorf("config validation failed: %w", err)
		}

		// Create AI client if not already set
		if s.aiClient == nil {
			aiClient, err := ai.New(cfg)
			if err != nil {
				return fmt.Errorf("failed to create AI client: %w", err)
			}
			s.aiClient = aiClient
		}

		// Create logger if not already set
		if s.logger == nil {
			loggerConfig := &logger.Config{
				Level:  config.ParseLogLevel(cfg.Logging.Level),
				Format: config.ParseLogFormat(cfg.Logging.Format),
				Output: os.Stderr,
			}
			s.logger = logger.New(loggerConfig).Named("service")
		}

		// Create observability provider if not already set and tracing is enabled
		if s.observabilityProvider == nil && cfg.Observability.TracingEnabled {
			observabilityConfig := observability.NewConfig(
				observability.WithServiceInfo(cfg.Observability.ServiceName, cfg.Observability.ServiceVersion, cfg.Server.Environment),
				observability.WithTracing(observability.TraceExporterStdout),
				observability.WithLogger(s.logger),
			)

			provider, err := observability.NewProvider(observabilityConfig)
			if err != nil {
				return fmt.Errorf("failed to create observability provider: %w", err)
			}
			s.observabilityProvider = provider
		}

		// Set default components if not already set
		if s.formatter == nil {
			s.formatter = formatter.New()
		}

		return nil
	}
}
