package service

import "resumatter/pkg/types"

// TailorResume options
func WithResumeData(data string) TailorResumeOption {
	return func(input *types.TailorResumeInput) error {
		input.BaseResume = data
		return nil
	}
}

func WithJobDescriptionData(data string) TailorResumeOption {
	return func(input *types.TailorResumeInput) error {
		input.JobDescription = data
		return nil
	}
}

// EvaluateResume options
func WithBaseResumeData(data string) EvaluateResumeOption {
	return func(input *types.EvaluateResumeInput) error {
		input.BaseResume = data
		return nil
	}
}

func WithTailoredResumeData(data string) EvaluateResumeOption {
	return func(input *types.EvaluateResumeInput) error {
		input.TailoredResume = data
		return nil
	}
}

// AnalyzeJob options
func WithJobDescriptionDataForAnalysis(data string) AnalyzeJobOption {
	return func(input *types.AnalyzeJobInput) error {
		input.JobDescription = data
		return nil
	}
}
