package config

import "time"

// OperationType represents the type of AI operation
type OperationType string

const (
	OperationTailor   OperationType = "tailor"
	OperationEvaluate OperationType = "evaluate"
	OperationAnalyze  OperationType = "analyze"
)

// ResolvedOperationConfig holds the final resolved configuration for an operation
type ResolvedOperationConfig struct {
	Provider    string
	Model       string
	Temperature float32
	MaxTokens   int
	Timeout     time.Duration
	APIKey      string

	// Circuit breaker settings
	CircuitBreakerEnabled          bool
	CircuitBreakerFailureThreshold float64
	CircuitBreakerMinRequests      uint32
	CircuitBreakerMaxRequests      uint32
	CircuitBreakerInterval         time.Duration
	CircuitBreakerTimeout          time.Duration
}

// ResolveOperationConfig resolves the final configuration for a specific operation
func (c *Config) ResolveOperationConfig(operation OperationType) (*ResolvedOperationConfig, error) {
	opConfig, err := c.GetOperationConfig(operation)
	if err != nil {
		return nil, err
	}

	// Parse timeout
	timeout, err := time.ParseDuration(opConfig.Timeout)
	if err != nil {
		return nil, err
	}

	// Parse circuit breaker intervals
	cbInterval, err := time.ParseDuration(opConfig.CircuitBreaker.Interval)
	if err != nil {
		return nil, err
	}

	cbTimeout, err := time.ParseDuration(opConfig.CircuitBreaker.Timeout)
	if err != nil {
		return nil, err
	}

	return &ResolvedOperationConfig{
		Provider:                       opConfig.Provider,
		Model:                          opConfig.Model,
		Temperature:                    opConfig.Temperature,
		MaxTokens:                      opConfig.MaxTokens,
		Timeout:                        timeout,
		APIKey:                         opConfig.APIKey,
		CircuitBreakerEnabled:          opConfig.CircuitBreaker.Enabled,
		CircuitBreakerFailureThreshold: opConfig.CircuitBreaker.FailureThreshold,
		CircuitBreakerMinRequests:      opConfig.CircuitBreaker.MinRequests,
		CircuitBreakerMaxRequests:      opConfig.CircuitBreaker.MaxRequests,
		CircuitBreakerInterval:         cbInterval,
		CircuitBreakerTimeout:          cbTimeout,
	}, nil
}
