//go:build fast

package config

import (
	"testing"

	"resumatter/pkg/logger"
)

func TestGetOperationConfig(t *testing.T) {
	tests := []struct {
		name      string
		config    *Config
		operation OperationType
		wantErr   bool
		expected  *OperationAIConfig
	}{
		{
			name: "valid tailor operation",
			config: &Config{
				AI: AIConfig{
					DefaultProvider:    "genai-gemini",
					DefaultModel:       "gemini-2.0-flash-lite",
					DefaultTemperature: 0.7,
					DefaultTimeout:     "30s",
					Operations: map[string]OperationAIConfig{
						"tailor": {
							Provider:    "genai-gemini",
							Model:       "gemini-2.0-flash",
							Temperature: 0.7,
							MaxTokens:   8192,
							Timeout:     "30s",
							APIKey:      "test-key",
						},
					},
				},
			},
			operation: OperationTailor,
			wantErr:   false,
			expected: &OperationAIConfig{
				Provider:    "genai-gemini",
				Model:       "gemini-2.0-flash",
				Temperature: 0.7,
				MaxTokens:   8192,
				Timeout:     "30s",
				APIKey:      "test-key",
			},
		},
		{
			name: "operation with defaults applied",
			config: &Config{
				AI: AIConfig{
					DefaultProvider:    "genai-gemini",
					DefaultModel:       "gemini-2.0-flash-lite",
					DefaultTemperature: 0.7,
					DefaultTimeout:     "30s",
					Operations: map[string]OperationAIConfig{
						"evaluate": {
							// Only specify temperature, others should use defaults
							Temperature: 0.3,
							APIKey:      "test-key",
						},
					},
				},
			},
			operation: OperationEvaluate,
			wantErr:   false,
			expected: &OperationAIConfig{
				Provider:    "genai-gemini",          // From default
				Model:       "gemini-2.0-flash-lite", // From default
				Temperature: 0.3,                     // Specified
				Timeout:     "30s",                   // From default
				APIKey:      "test-key",
			},
		},
		{
			name: "missing operation",
			config: &Config{
				AI: AIConfig{
					Operations: map[string]OperationAIConfig{},
				},
			},
			operation: OperationTailor,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.config.GetOperationConfig(tt.operation)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetOperationConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if result.Provider != tt.expected.Provider {
					t.Errorf("Provider = %v, want %v", result.Provider, tt.expected.Provider)
				}
				if result.Model != tt.expected.Model {
					t.Errorf("Model = %v, want %v", result.Model, tt.expected.Model)
				}
				if result.Temperature != tt.expected.Temperature {
					t.Errorf("Temperature = %v, want %v", result.Temperature, tt.expected.Temperature)
				}
				if result.Timeout != tt.expected.Timeout {
					t.Errorf("Timeout = %v, want %v", result.Timeout, tt.expected.Timeout)
				}
			}
		})
	}
}

func TestValidateOperationConfigs(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config with all operations",
			config: &Config{
				AI: AIConfig{
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "missing tailor operation",
			config: &Config{
				AI: AIConfig{
					Operations: map[string]OperationAIConfig{
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "missing evaluate operation",
			config: &Config{
				AI: AIConfig{
					Operations: map[string]OperationAIConfig{
						"tailor":  {Provider: "genai-gemini", Model: "test-model"},
						"analyze": {Provider: "genai-gemini", Model: "test-model"},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "missing analyze operation",
			config: &Config{
				AI: AIConfig{
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
					},
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.ValidateOperationConfigs()
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateOperationConfigs() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidate(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid complete config",
			config: &Config{
				AI: AIConfig{
					DefaultProvider:    "genai-gemini",
					DefaultModel:       "gemini-2.0-flash-lite",
					DefaultTemperature: 0.7,
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
				Server: ServerConfig{
					Port: "8080",
				},
				Auth: AuthConfig{
					Enabled: false,
				},
			},
			wantErr: false,
		},
		{
			name: "missing default provider",
			config: &Config{
				AI: AIConfig{
					DefaultModel: "test-model",
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
				Server: ServerConfig{Port: "8080"},
				Auth:   AuthConfig{Enabled: false},
			},
			wantErr: true,
		},
		{
			name: "missing default model",
			config: &Config{
				AI: AIConfig{
					DefaultProvider: "genai-gemini",
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
				Server: ServerConfig{Port: "8080"},
				Auth:   AuthConfig{Enabled: false},
			},
			wantErr: true,
		},
		{
			name: "missing server port",
			config: &Config{
				AI: AIConfig{
					DefaultProvider: "genai-gemini",
					DefaultModel:    "test-model",
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
				Server: ServerConfig{Port: ""},
				Auth:   AuthConfig{Enabled: false},
			},
			wantErr: true,
		},
		{
			name: "auth enabled but no tokens",
			config: &Config{
				AI: AIConfig{
					DefaultProvider: "genai-gemini",
					DefaultModel:    "test-model",
					Operations: map[string]OperationAIConfig{
						"tailor":   {Provider: "genai-gemini", Model: "test-model"},
						"evaluate": {Provider: "genai-gemini", Model: "test-model"},
						"analyze":  {Provider: "genai-gemini", Model: "test-model"},
					},
				},
				Server: ServerConfig{Port: "8080"},
				Auth: AuthConfig{
					Enabled: true,
					APIKeys: []string{}, // Empty API keys should cause validation error
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestParseLogLevel(t *testing.T) {
	tests := []struct {
		input    string
		expected logger.Level
	}{
		{"debug", logger.LevelDebug},
		{"info", logger.LevelInfo},
		{"warn", logger.LevelWarn},
		{"warning", logger.LevelWarn},
		{"error", logger.LevelError},
		{"invalid", logger.LevelInfo}, // default
		{"", logger.LevelInfo},        // default
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := ParseLogLevel(tt.input)
			if result != tt.expected {
				t.Errorf("ParseLogLevel(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestParseLogFormat(t *testing.T) {
	tests := []struct {
		input    string
		expected logger.Format
	}{
		{"json", logger.FormatJSON},
		{"text", logger.FormatText},
		{"invalid", logger.FormatText}, // default
		{"", logger.FormatText},        // default
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := ParseLogFormat(tt.input)
			if result != tt.expected {
				t.Errorf("ParseLogFormat(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}
