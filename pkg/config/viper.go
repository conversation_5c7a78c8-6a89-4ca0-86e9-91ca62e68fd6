package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Provider manages application configuration using Viper
type Provider struct {
	viper *viper.Viper
}

// NewProvider creates a new configuration provider
func NewProvider() *Provider {
	v := viper.New()

	// Set configuration file properties
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath("/etc/resumatter/")
	v.AddConfigPath("$HOME/.resumatter")
	v.AddConfigPath(".")

	// Set environment variable properties
	v.SetEnvPrefix("RESUMATTER")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_"))
	v.AutomaticEnv()

	// Set defaults
	setDefaults(v)

	return &Provider{viper: v}
}

// Load loads configuration from all sources (files, env vars, defaults)
func (p *Provider) Load() (*Config, error) {
	// Try to read configuration file (optional)
	if err := p.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found is OK, we'll use env vars and defaults
	}

	var config Config
	if err := p.viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// AI defaults
	v.SetDefault("ai.default_provider", "genai-gemini")
	v.SetDefault("ai.default_model", "gemini-2.0-flash-lite")
	v.SetDefault("ai.default_temperature", 0.7)
	v.SetDefault("ai.default_timeout", "30s")

	// Operation-specific AI defaults
	// Tailor operation - optimized for creative resume tailoring
	v.SetDefault("ai.operations.tailor.provider", "")
	v.SetDefault("ai.operations.tailor.model", "")
	v.SetDefault("ai.operations.tailor.temperature", 0.7)
	v.SetDefault("ai.operations.tailor.max_tokens", 8192)
	v.SetDefault("ai.operations.tailor.timeout", "30s")
	v.SetDefault("ai.operations.tailor.circuit_breaker.enabled", true)
	v.SetDefault("ai.operations.tailor.circuit_breaker.failure_threshold", 0.6)
	v.SetDefault("ai.operations.tailor.circuit_breaker.min_requests", 5)
	v.SetDefault("ai.operations.tailor.circuit_breaker.max_requests", 3)
	v.SetDefault("ai.operations.tailor.circuit_breaker.interval", "60s")
	v.SetDefault("ai.operations.tailor.circuit_breaker.timeout", "30s")

	// Evaluate operation - lower temperature for consistent evaluation
	v.SetDefault("ai.operations.evaluate.provider", "")
	v.SetDefault("ai.operations.evaluate.model", "")
	v.SetDefault("ai.operations.evaluate.temperature", 0.3)
	v.SetDefault("ai.operations.evaluate.max_tokens", 8192)
	v.SetDefault("ai.operations.evaluate.timeout", "30s")
	v.SetDefault("ai.operations.evaluate.circuit_breaker.enabled", true)
	v.SetDefault("ai.operations.evaluate.circuit_breaker.failure_threshold", 0.6)
	v.SetDefault("ai.operations.evaluate.circuit_breaker.min_requests", 5)
	v.SetDefault("ai.operations.evaluate.circuit_breaker.max_requests", 3)
	v.SetDefault("ai.operations.evaluate.circuit_breaker.interval", "60s")
	v.SetDefault("ai.operations.evaluate.circuit_breaker.timeout", "30s")

	// Analyze operation - balanced settings for job analysis
	v.SetDefault("ai.operations.analyze.provider", "")
	v.SetDefault("ai.operations.analyze.model", "")
	v.SetDefault("ai.operations.analyze.temperature", 0.5)
	v.SetDefault("ai.operations.analyze.max_tokens", 8192)
	v.SetDefault("ai.operations.analyze.timeout", "30s")
	v.SetDefault("ai.operations.analyze.circuit_breaker.enabled", true)
	v.SetDefault("ai.operations.analyze.circuit_breaker.failure_threshold", 0.6)
	v.SetDefault("ai.operations.analyze.circuit_breaker.min_requests", 5)
	v.SetDefault("ai.operations.analyze.circuit_breaker.max_requests", 3)
	v.SetDefault("ai.operations.analyze.circuit_breaker.interval", "60s")
	v.SetDefault("ai.operations.analyze.circuit_breaker.timeout", "30s")

	// Provider defaults
	v.SetDefault("providers.genai.credentials_file", "")
	v.SetDefault("providers.genai.google_cloud_project", "")
	v.SetDefault("providers.genai.google_cloud_region", "us-central1")

	// Server defaults
	v.SetDefault("server.port", "8080")
	v.SetDefault("server.environment", "development")

	// Auth defaults
	v.SetDefault("auth.enabled", false)
	v.SetDefault("auth.api_keys", []string{})
	v.SetDefault("auth.logging_only", true)

	// Logging defaults
	v.SetDefault("logging.level", "info")
	v.SetDefault("logging.format", "text")

	// Observability defaults
	v.SetDefault("observability.tracing_enabled", false)
	v.SetDefault("observability.service_name", "resumatter")
	v.SetDefault("observability.service_version", "dev")

	// Rate limiting defaults
	v.SetDefault("rate_limit.enabled", false)
	v.SetDefault("rate_limit.backend", "memory")
	v.SetDefault("rate_limit.strategy", "token_bucket")
	v.SetDefault("rate_limit.key_by", "ip")
	v.SetDefault("rate_limit.header_name", "X-Client-ID")

	// Rate limiting default limits
	v.SetDefault("rate_limit.defaults.requests_per_minute", 60)
	v.SetDefault("rate_limit.defaults.requests_per_hour", 1000)
	v.SetDefault("rate_limit.defaults.requests_per_day", 10000)
	v.SetDefault("rate_limit.defaults.burst_size", 10)

	// Rate limiting Redis defaults
	v.SetDefault("rate_limit.redis.address", "localhost:6379")
	v.SetDefault("rate_limit.redis.password", "")
	v.SetDefault("rate_limit.redis.db", 0)
	v.SetDefault("rate_limit.redis.pool_size", 10)

	// Per-operation rate limiting defaults
	// Tailor operation - AI operations are expensive, lower limits
	v.SetDefault("rate_limit.operations.tailor.enabled", true)
	v.SetDefault("rate_limit.operations.tailor.requests_per_minute", 10)
	v.SetDefault("rate_limit.operations.tailor.requests_per_hour", 100)
	v.SetDefault("rate_limit.operations.tailor.requests_per_day", 1000)
	v.SetDefault("rate_limit.operations.tailor.burst_size", 5)

	// Evaluate operation - moderate limits
	v.SetDefault("rate_limit.operations.evaluate.enabled", true)
	v.SetDefault("rate_limit.operations.evaluate.requests_per_minute", 15)
	v.SetDefault("rate_limit.operations.evaluate.requests_per_hour", 150)
	v.SetDefault("rate_limit.operations.evaluate.requests_per_day", 1500)
	v.SetDefault("rate_limit.operations.evaluate.burst_size", 5)

	// Analyze operation - lighter operation, higher limits
	v.SetDefault("rate_limit.operations.analyze.enabled", true)
	v.SetDefault("rate_limit.operations.analyze.requests_per_minute", 20)
	v.SetDefault("rate_limit.operations.analyze.requests_per_hour", 200)
	v.SetDefault("rate_limit.operations.analyze.requests_per_day", 2000)
	v.SetDefault("rate_limit.operations.analyze.burst_size", 8)

	// Environment variable mappings
	if err := v.BindEnv("ai.operations.tailor.api_key", "GEMINI_API_KEY"); err != nil {
		// Log error but continue - BindEnv errors are rare and non-fatal
		fmt.Printf("Warning: failed to bind environment variable for ai.operations.tailor.api_key: %v\n", err)
	}
	if err := v.BindEnv("ai.operations.evaluate.api_key", "GEMINI_API_KEY"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for ai.operations.evaluate.api_key: %v\n", err)
	}
	if err := v.BindEnv("ai.operations.analyze.api_key", "GEMINI_API_KEY"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for ai.operations.analyze.api_key: %v\n", err)
	}
	if err := v.BindEnv("providers.genai.google_cloud_project", "GOOGLE_CLOUD_PROJECT"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for providers.genai.google_cloud_project: %v\n", err)
	}
	if err := v.BindEnv("providers.genai.google_cloud_region", "GOOGLE_CLOUD_REGION"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for providers.genai.google_cloud_region: %v\n", err)
	}
	if err := v.BindEnv("observability.tracing_enabled", "OTEL_TRACING_ENABLED"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for observability.tracing_enabled: %v\n", err)
	}
	if err := v.BindEnv("observability.service_name", "OTEL_SERVICE_NAME"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for observability.service_name: %v\n", err)
	}
	if err := v.BindEnv("observability.service_version", "OTEL_SERVICE_VERSION"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for observability.service_version: %v\n", err)
	}
	if err := v.BindEnv("server.environment", "ENVIRONMENT"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for server.environment: %v\n", err)
	}
	if err := v.BindEnv("server.port", "PORT"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for server.port: %v\n", err)
	}

	// Redis configuration - bind to standard Redis environment variables
	if err := v.BindEnv("rate_limit.redis.address", "REDIS_ADDRESS"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for rate_limit.redis.address: %v\n", err)
	}
	if err := v.BindEnv("rate_limit.redis.password", "REDIS_PASSWORD"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for rate_limit.redis.password: %v\n", err)
	}
	if err := v.BindEnv("rate_limit.redis.db", "REDIS_DB"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for rate_limit.redis.db: %v\n", err)
	}
	if err := v.BindEnv("rate_limit.redis.pool_size", "REDIS_POOL_SIZE"); err != nil {
		fmt.Printf("Warning: failed to bind environment variable for rate_limit.redis.pool_size: %v\n", err)
	}
}

// LoadConfig loads configuration using the default provider
func LoadConfig() (*Config, error) {
	provider := NewProvider()
	return provider.Load()
}
