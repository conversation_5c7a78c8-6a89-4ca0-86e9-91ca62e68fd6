//go:build fast

package config

import (
	"testing"
)

func TestConfig_ValidateRateLimitConfig_Disabled(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled: false,
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err != nil {
		t.Errorf("Expected no error when rate limiting is disabled, got: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_ValidConfig(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
			Operations: map[string]OperationRateLimit{
				"tailor": {
					Enabled:           true,
					RequestsPerMinute: 10,
					RequestsPerHour:   100,
					RequestsPerDay:    1000,
					BurstSize:         5,
				},
			},
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err != nil {
		t.Errorf("Expected no error for valid config, got: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_InvalidBackend(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "invalid",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err == nil {
		t.Error("Expected error for invalid backend")
	}
	if err != nil && err.Error() != "invalid rate limit backend 'invalid', must be one of: [memory redis]" {
		t.Errorf("Unexpected error message: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_InvalidStrategy(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "invalid",
			KeyBy:    "ip",
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err == nil {
		t.Error("Expected error for invalid strategy")
	}
	if err != nil && err.Error() != "invalid rate limit strategy 'invalid', must be one of: [token_bucket sliding_window fixed_window]" {
		t.Errorf("Unexpected error message: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_InvalidKeyBy(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "invalid",
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err == nil {
		t.Error("Expected error for invalid key_by")
	}
	if err != nil && err.Error() != "invalid rate limit key_by 'invalid', must be one of: [ip api_key header]" {
		t.Errorf("Unexpected error message: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_HeaderKeyByMissingHeaderName(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:    true,
			Backend:    "memory",
			Strategy:   "token_bucket",
			KeyBy:      "header",
			HeaderName: "", // Missing header name
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err == nil {
		t.Error("Expected error for missing header_name when key_by is 'header'")
	}
	if err != nil && err.Error() != "rate limit header_name cannot be empty when key_by is 'header'" {
		t.Errorf("Unexpected error message: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_RedisBackendMissingAddress(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "redis",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Redis: RedisConfig{
				Address: "", // Missing address
			},
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
		},
	}

	err := cfg.ValidateRateLimitConfig()
	if err == nil {
		t.Error("Expected error for missing redis address")
	}
	if err != nil && err.Error() != "redis address cannot be empty when using redis backend" {
		t.Errorf("Unexpected error message: %v", err)
	}
}

func TestConfig_ValidateRateLimitConfig_InvalidLimits(t *testing.T) {
	tests := []struct {
		name        string
		config      RateLimitConfig
		expectedErr string
	}{
		{
			name: "zero requests per minute",
			config: RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "token_bucket",
				KeyBy:    "ip",
				Defaults: DefaultRateLimit{
					RequestsPerMinute: 0, // Invalid
					RequestsPerHour:   1000,
					RequestsPerDay:    10000,
					BurstSize:         10,
				},
			},
			expectedErr: "defaults requests_per_minute must be greater than 0",
		},
		{
			name: "inconsistent hour vs minute limits",
			config: RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "token_bucket",
				KeyBy:    "ip",
				Defaults: DefaultRateLimit{
					RequestsPerMinute: 100,
					RequestsPerHour:   50, // Less than minute limit
					RequestsPerDay:    10000,
					BurstSize:         10,
				},
			},
			expectedErr: "defaults requests_per_hour (50) should be >= requests_per_minute (100)",
		},
		{
			name: "excessive burst size",
			config: RateLimitConfig{
				Enabled:  true,
				Backend:  "memory",
				Strategy: "token_bucket",
				KeyBy:    "ip",
				Defaults: DefaultRateLimit{
					RequestsPerMinute: 10,
					RequestsPerHour:   1000,
					RequestsPerDay:    10000,
					BurstSize:         25, // More than 2x requests per minute
				},
			},
			expectedErr: "defaults burst_size (25) should not exceed 2x requests_per_minute (10)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &Config{RateLimit: tt.config}
			err := cfg.ValidateRateLimitConfig()
			if err == nil {
				t.Error("Expected error for invalid limits")
			}
			if err != nil && err.Error() != tt.expectedErr {
				t.Errorf("Expected error '%s', got '%s'", tt.expectedErr, err.Error())
			}
		})
	}
}

func TestConfig_GetOperationRateLimit(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
			Operations: map[string]OperationRateLimit{
				"tailor": {
					Enabled:           true,
					RequestsPerMinute: 10,
					RequestsPerHour:   100,
					RequestsPerDay:    1000,
					BurstSize:         5,
				},
			},
		},
	}

	// Test existing operation
	opConfig, err := cfg.GetOperationRateLimit("tailor")
	if err != nil {
		t.Errorf("Expected no error for existing operation, got: %v", err)
	}
	if opConfig.RequestsPerMinute != 10 {
		t.Errorf("Expected 10 requests per minute, got: %d", opConfig.RequestsPerMinute)
	}

	// Test non-existing operation (should return defaults)
	opConfig, err = cfg.GetOperationRateLimit("nonexistent")
	if err != nil {
		t.Errorf("Expected no error for non-existing operation, got: %v", err)
	}
	if opConfig.RequestsPerMinute != 60 {
		t.Errorf("Expected default 60 requests per minute, got: %d", opConfig.RequestsPerMinute)
	}

	// Test with rate limiting disabled
	cfg.RateLimit.Enabled = false
	_, err = cfg.GetOperationRateLimit("tailor")
	if err == nil {
		t.Error("Expected error when rate limiting is disabled")
	}
}

func TestConfig_GetOperationRateLimit_ApplyDefaults(t *testing.T) {
	cfg := &Config{
		RateLimit: RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
			Operations: map[string]OperationRateLimit{
				"partial": {
					Enabled:           true,
					RequestsPerMinute: 20,
					// Other fields missing, should use defaults
				},
			},
		},
	}

	opConfig, err := cfg.GetOperationRateLimit("partial")
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if opConfig.RequestsPerMinute != 20 {
		t.Errorf("Expected 20 requests per minute, got: %d", opConfig.RequestsPerMinute)
	}
	if opConfig.RequestsPerHour != 1000 {
		t.Errorf("Expected default 1000 requests per hour, got: %d", opConfig.RequestsPerHour)
	}
	if opConfig.RequestsPerDay != 10000 {
		t.Errorf("Expected default 10000 requests per day, got: %d", opConfig.RequestsPerDay)
	}
	if opConfig.BurstSize != 10 {
		t.Errorf("Expected default 10 burst size, got: %d", opConfig.BurstSize)
	}
}
