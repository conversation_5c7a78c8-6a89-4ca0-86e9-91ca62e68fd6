package config

import (
	"fmt"
	"slices"

	"resumatter/pkg/logger"
)

// Config holds the application configuration
type Config struct {
	AI            AIConfig            `mapstructure:"ai"`
	Providers     ProviderConfig      `mapstructure:"providers"`
	Server        ServerConfig        `mapstructure:"server"`
	Auth          AuthConfig          `mapstructure:"auth"`
	RateLimit     RateLimitConfig     `mapstructure:"rate_limit"`
	Logging       LoggingConfig       `mapstructure:"logging"`
	Observability ObservabilityConfig `mapstructure:"observability"`
}

// AIConfig holds AI configuration
type AIConfig struct {
	DefaultProvider    string                       `mapstructure:"default_provider"`
	DefaultModel       string                       `mapstructure:"default_model"`
	DefaultTemperature float32                      `mapstructure:"default_temperature"`
	DefaultTimeout     string                       `mapstructure:"default_timeout"`
	Operations         map[string]OperationAIConfig `mapstructure:"operations"`
}

// OperationAIConfig holds operation-specific AI configuration
type OperationAIConfig struct {
	Provider       string               `mapstructure:"provider"`
	Model          string               `mapstructure:"model"`
	Temperature    float32              `mapstructure:"temperature"`
	MaxTokens      int                  `mapstructure:"max_tokens"`
	Timeout        string               `mapstructure:"timeout"`
	APIKey         string               `mapstructure:"api_key"`
	CircuitBreaker CircuitBreakerConfig `mapstructure:"circuit_breaker"`
	ProviderConfig map[string]any       `mapstructure:"provider_config"`
}

// ProviderConfig holds provider-specific configurations
type ProviderConfig struct {
	GenAI     GenAIConfig     `mapstructure:"genai"`
	OpenAI    OpenAIConfig    `mapstructure:"openai"`
	Anthropic AnthropicConfig `mapstructure:"anthropic"`
}

// GenAIConfig holds unified GenAI configuration for both Gemini API and Vertex AI
type GenAIConfig struct {
	Backend            string `mapstructure:"backend"`
	CredentialsFile    string `mapstructure:"credentials_file"`
	GoogleCloudProject string `mapstructure:"google_cloud_project"`
	GoogleCloudRegion  string `mapstructure:"google_cloud_region"`
}

// OpenAIConfig holds OpenAI configuration
type OpenAIConfig struct {
	OrganizationID string `mapstructure:"organization_id"`
	BaseURL        string `mapstructure:"base_url"`
}

// AnthropicConfig holds Anthropic configuration
type AnthropicConfig struct {
	BaseURL string `mapstructure:"base_url"`
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port        string `mapstructure:"port"`
	Environment string `mapstructure:"environment"`
}

// AuthConfig holds authentication configuration
// Add APIKeys for HTTP API key authentication
type AuthConfig struct {
	Enabled     bool     `mapstructure:"enabled"`
	LoggingOnly bool     `mapstructure:"logging_only"`
	APIKeys     []string `mapstructure:"api_keys"`
}

// CircuitBreakerConfig holds circuit breaker configuration
type CircuitBreakerConfig struct {
	Enabled          bool    `mapstructure:"enabled"`
	FailureThreshold float64 `mapstructure:"failure_threshold"`
	MinRequests      uint32  `mapstructure:"min_requests"`
	MaxRequests      uint32  `mapstructure:"max_requests"`
	Interval         string  `mapstructure:"interval"`
	Timeout          string  `mapstructure:"timeout"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

// ObservabilityConfig holds observability configuration
type ObservabilityConfig struct {
	TracingEnabled bool   `mapstructure:"tracing_enabled"`
	ServiceName    string `mapstructure:"service_name"`
	ServiceVersion string `mapstructure:"service_version"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled    bool                          `mapstructure:"enabled"`
	Backend    string                        `mapstructure:"backend"`     // "memory", "redis"
	Strategy   string                        `mapstructure:"strategy"`    // "token_bucket", "sliding_window", "fixed_window"
	KeyBy      string                        `mapstructure:"key_by"`      // "ip", "api_key", "header"
	HeaderName string                        `mapstructure:"header_name"` // Custom header for identification
	Operations map[string]OperationRateLimit `mapstructure:"operations"`
	Redis      RedisConfig                   `mapstructure:"redis"`
	Defaults   DefaultRateLimit              `mapstructure:"defaults"`
}

// OperationRateLimit holds per-operation rate limit settings
type OperationRateLimit struct {
	RequestsPerMinute int  `mapstructure:"requests_per_minute"`
	RequestsPerHour   int  `mapstructure:"requests_per_hour"`
	RequestsPerDay    int  `mapstructure:"requests_per_day"`
	BurstSize         int  `mapstructure:"burst_size"`
	Enabled           bool `mapstructure:"enabled"`
}

// DefaultRateLimit holds default rate limit settings
type DefaultRateLimit struct {
	RequestsPerMinute int `mapstructure:"requests_per_minute"`
	RequestsPerHour   int `mapstructure:"requests_per_hour"`
	RequestsPerDay    int `mapstructure:"requests_per_day"`
	BurstSize         int `mapstructure:"burst_size"`
}

// RedisConfig holds Redis configuration for rate limiting
type RedisConfig struct {
	Address  string `mapstructure:"address"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// GetOperationConfig returns the configuration for a specific operation
func (c *Config) GetOperationConfig(operation OperationType) (*OperationAIConfig, error) {
	opConfig, exists := c.AI.Operations[string(operation)]
	if !exists {
		return nil, fmt.Errorf("no configuration found for operation: %s", operation)
	}

	// Apply defaults where not specified
	if opConfig.Provider == "" {
		opConfig.Provider = c.AI.DefaultProvider
	}
	if opConfig.Model == "" {
		opConfig.Model = c.AI.DefaultModel
	}
	if opConfig.Temperature == 0 {
		opConfig.Temperature = c.AI.DefaultTemperature
	}
	if opConfig.Timeout == "" {
		opConfig.Timeout = c.AI.DefaultTimeout
	}

	return &opConfig, nil
}

// ValidateOperationConfigs validates that all required operations are configured
func (c *Config) ValidateOperationConfigs() error {
	requiredOps := []OperationType{OperationTailor, OperationEvaluate, OperationAnalyze}
	supportedProviders := []string{"genai-gemini", "genai-vertexai"}

	for _, op := range requiredOps {
		opConfig, exists := c.AI.Operations[string(op)]
		if !exists {
			return fmt.Errorf("missing required operation configuration: %s", op)
		}

		// Validate provider for each operation
		if opConfig.Provider != "" && !slices.Contains(supportedProviders, opConfig.Provider) {
			return fmt.Errorf("unsupported provider '%s' for operation '%s', supported providers are: %v", opConfig.Provider, op, supportedProviders)
		}
	}

	return nil
}

// Validate validates the entire configuration
func (c *Config) Validate() error {
	// Validate AI configuration
	if c.AI.DefaultProvider == "" {
		return fmt.Errorf("default AI provider is required")
	}
	if c.AI.DefaultModel == "" {
		return fmt.Errorf("default AI model is required")
	}

	// Validate provider is supported
	supportedProviders := []string{"genai-gemini", "genai-vertexai"}
	if !slices.Contains(supportedProviders, c.AI.DefaultProvider) {
		return fmt.Errorf("unsupported default provider '%s', supported providers are: %v", c.AI.DefaultProvider, supportedProviders)
	}

	// Validate operation configurations
	if err := c.ValidateOperationConfigs(); err != nil {
		return fmt.Errorf("operation config validation failed: %w", err)
	}

	// Validate server configuration
	if c.Server.Port == "" {
		return fmt.Errorf("server port cannot be empty")
	}

	// Validate auth configuration
	if c.Auth.Enabled && len(c.Auth.APIKeys) == 0 {
		return fmt.Errorf("authentication is enabled but no api keys are configured")
	}

	// Validate rate limiting configuration
	if err := c.ValidateRateLimitConfig(); err != nil {
		return fmt.Errorf("rate limit config validation failed: %w", err)
	}

	return nil
}

// ValidateRateLimitConfig validates rate limiting configuration
func (c *Config) ValidateRateLimitConfig() error {
	if !c.RateLimit.Enabled {
		return nil // Skip validation if rate limiting is disabled
	}

	// Validate backend
	validBackends := []string{"memory", "redis"}
	if c.RateLimit.Backend == "" {
		return fmt.Errorf("rate limit backend cannot be empty when rate limiting is enabled")
	}
	backendValid := slices.Contains(validBackends, c.RateLimit.Backend)
	if !backendValid {
		return fmt.Errorf("invalid rate limit backend '%s', must be one of: %v", c.RateLimit.Backend, validBackends)
	}

	// Validate strategy
	validStrategies := []string{"token_bucket", "sliding_window", "fixed_window"}
	if c.RateLimit.Strategy == "" {
		return fmt.Errorf("rate limit strategy cannot be empty when rate limiting is enabled")
	}
	strategyValid := slices.Contains(validStrategies, c.RateLimit.Strategy)
	if !strategyValid {
		return fmt.Errorf("invalid rate limit strategy '%s', must be one of: %v", c.RateLimit.Strategy, validStrategies)
	}

	// Validate key_by
	validKeyBy := []string{"ip", "api_key", "header"}
	if c.RateLimit.KeyBy == "" {
		return fmt.Errorf("rate limit key_by cannot be empty when rate limiting is enabled")
	}
	keyByValid := slices.Contains(validKeyBy, c.RateLimit.KeyBy)
	if !keyByValid {
		return fmt.Errorf("invalid rate limit key_by '%s', must be one of: %v", c.RateLimit.KeyBy, validKeyBy)
	}

	// Validate header name if key_by is "header"
	if c.RateLimit.KeyBy == "header" && c.RateLimit.HeaderName == "" {
		return fmt.Errorf("rate limit header_name cannot be empty when key_by is 'header'")
	}

	// Validate Redis configuration if backend is "redis"
	if c.RateLimit.Backend == "redis" {
		if c.RateLimit.Redis.Address == "" {
			return fmt.Errorf("redis address cannot be empty when using redis backend")
		}
		if c.RateLimit.Redis.PoolSize <= 0 {
			return fmt.Errorf("redis pool_size must be greater than 0")
		}
	}

	// Validate default limits
	if err := c.validateRateLimits("defaults", c.RateLimit.Defaults.RequestsPerMinute, c.RateLimit.Defaults.RequestsPerHour, c.RateLimit.Defaults.RequestsPerDay, c.RateLimit.Defaults.BurstSize); err != nil {
		return err
	}

	// Validate operation-specific limits
	for opName, opConfig := range c.RateLimit.Operations {
		if opConfig.Enabled {
			if err := c.validateRateLimits(opName, opConfig.RequestsPerMinute, opConfig.RequestsPerHour, opConfig.RequestsPerDay, opConfig.BurstSize); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateRateLimits validates rate limit values for consistency
func (c *Config) validateRateLimits(name string, rpm, rph, rpd, burst int) error {
	if rpm <= 0 {
		return fmt.Errorf("%s requests_per_minute must be greater than 0", name)
	}
	if rph <= 0 {
		return fmt.Errorf("%s requests_per_hour must be greater than 0", name)
	}
	if rpd <= 0 {
		return fmt.Errorf("%s requests_per_day must be greater than 0", name)
	}
	if burst <= 0 {
		return fmt.Errorf("%s burst_size must be greater than 0", name)
	}

	// Validate that limits are consistent (hour >= minute * 60, day >= hour * 24)
	if rph < rpm {
		return fmt.Errorf("%s requests_per_hour (%d) should be >= requests_per_minute (%d)", name, rph, rpm)
	}
	if rpd < rph {
		return fmt.Errorf("%s requests_per_day (%d) should be >= requests_per_hour (%d)", name, rpd, rph)
	}

	// Burst size should be reasonable compared to per-minute limit
	if burst > rpm*2 {
		return fmt.Errorf("%s burst_size (%d) should not exceed 2x requests_per_minute (%d)", name, burst, rpm)
	}

	return nil
}

// GetOperationRateLimit returns rate limit configuration for a specific operation
func (c *Config) GetOperationRateLimit(operation string) (*OperationRateLimit, error) {
	if !c.RateLimit.Enabled {
		return nil, fmt.Errorf("rate limiting is disabled")
	}

	opConfig, exists := c.RateLimit.Operations[operation]
	if !exists {
		// Return default limits if operation-specific config doesn't exist
		return &OperationRateLimit{
			RequestsPerMinute: c.RateLimit.Defaults.RequestsPerMinute,
			RequestsPerHour:   c.RateLimit.Defaults.RequestsPerHour,
			RequestsPerDay:    c.RateLimit.Defaults.RequestsPerDay,
			BurstSize:         c.RateLimit.Defaults.BurstSize,
			Enabled:           true,
		}, nil
	}

	// Apply defaults where not specified
	if opConfig.RequestsPerMinute == 0 {
		opConfig.RequestsPerMinute = c.RateLimit.Defaults.RequestsPerMinute
	}
	if opConfig.RequestsPerHour == 0 {
		opConfig.RequestsPerHour = c.RateLimit.Defaults.RequestsPerHour
	}
	if opConfig.RequestsPerDay == 0 {
		opConfig.RequestsPerDay = c.RateLimit.Defaults.RequestsPerDay
	}
	if opConfig.BurstSize == 0 {
		opConfig.BurstSize = c.RateLimit.Defaults.BurstSize
	}

	return &opConfig, nil
}

// ParseLogLevel parses the log level from string
func ParseLogLevel(level string) logger.Level {
	switch level {
	case "debug":
		return logger.LevelDebug
	case "info":
		return logger.LevelInfo
	case "warn", "warning":
		return logger.LevelWarn
	case "error":
		return logger.LevelError
	default:
		return logger.LevelInfo
	}
}

// ParseLogFormat parses the log format from string
func ParseLogFormat(format string) logger.Format {
	switch format {
	case "json":
		return logger.FormatJSON
	case "text":
		return logger.FormatText
	default:
		return logger.FormatText
	}
}
