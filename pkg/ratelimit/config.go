package ratelimit

import (
	"fmt"
	"slices"
	"time"

	"resumatter/pkg/config"
	"resumatter/pkg/ratelimit/backends"
	"resumatter/pkg/ratelimit/strategies"
)

// Config holds the resolved rate limiting configuration
type Config struct {
	// Enabled indicates if rate limiting is enabled
	Enabled bool

	// Backend configuration
	Backend backends.BackendConfig

	// Strategy configuration
	Strategy strategies.StrategyConfig

	// KeyBy specifies how to identify clients
	KeyBy string

	// HeaderName is the header name when KeyBy is "header"
	HeaderName string

	// Operations holds per-operation rate limits
	Operations map[string]*OperationConfig

	// Defaults holds default rate limits
	Defaults *OperationConfig
}

// OperationConfig holds rate limiting configuration for a specific operation
type OperationConfig struct {
	// Enabled indicates if rate limiting is enabled for this operation
	Enabled bool

	// RequestsPerMinute is the number of requests allowed per minute
	RequestsPerMinute int64

	// RequestsPerHour is the number of requests allowed per hour
	RequestsPerHour int64

	// RequestsPerDay is the number of requests allowed per day
	RequestsPerDay int64

	// BurstSize is the maximum burst size allowed
	BurstSize int64

	// WindowDuration is the primary window duration for rate limiting
	WindowDuration time.Duration

	// Limit is the resolved limit for the primary window
	Limit int64
}

// ResolveConfig converts the main application config to rate limiting config
func ResolveConfig(appConfig *config.Config) (*Config, error) {
	if !appConfig.RateLimit.Enabled {
		return &Config{Enabled: false}, nil
	}

	// Resolve backend configuration
	backendConfig := backends.BackendConfig{
		Type:            appConfig.RateLimit.Backend,
		RedisAddress:    appConfig.RateLimit.Redis.Address,
		RedisPassword:   appConfig.RateLimit.Redis.Password,
		RedisDB:         appConfig.RateLimit.Redis.DB,
		RedisPoolSize:   appConfig.RateLimit.Redis.PoolSize,
		CleanupInterval: 5 * time.Minute, // Default cleanup interval for memory backend
		MaxKeys:         10000,           // Default max keys for memory backend
	}

	// Resolve strategy configuration
	strategyConfig := strategies.StrategyConfig{
		Type: appConfig.RateLimit.Strategy,
	}

	// Set strategy-specific defaults
	switch appConfig.RateLimit.Strategy {
	case "token_bucket":
		strategyConfig.RefillRate = time.Minute
		strategyConfig.RefillAmount = 1
	case "sliding_window":
		strategyConfig.WindowSize = time.Minute
		strategyConfig.Precision = 10 * time.Second
	case "fixed_window":
		strategyConfig.WindowDuration = time.Minute
	}

	// Resolve default operation configuration
	defaults := &OperationConfig{
		Enabled:           true,
		RequestsPerMinute: int64(appConfig.RateLimit.Defaults.RequestsPerMinute),
		RequestsPerHour:   int64(appConfig.RateLimit.Defaults.RequestsPerHour),
		RequestsPerDay:    int64(appConfig.RateLimit.Defaults.RequestsPerDay),
		BurstSize:         int64(appConfig.RateLimit.Defaults.BurstSize),
		WindowDuration:    time.Minute,
		Limit:             int64(appConfig.RateLimit.Defaults.RequestsPerMinute),
	}

	// Resolve per-operation configurations
	operations := make(map[string]*OperationConfig)
	for opName, opConfig := range appConfig.RateLimit.Operations {
		if !opConfig.Enabled {
			continue
		}

		// Apply defaults where not specified
		rpm := opConfig.RequestsPerMinute
		if rpm == 0 {
			rpm = appConfig.RateLimit.Defaults.RequestsPerMinute
		}

		rph := opConfig.RequestsPerHour
		if rph == 0 {
			rph = appConfig.RateLimit.Defaults.RequestsPerHour
		}

		rpd := opConfig.RequestsPerDay
		if rpd == 0 {
			rpd = appConfig.RateLimit.Defaults.RequestsPerDay
		}

		burstSize := opConfig.BurstSize
		if burstSize == 0 {
			burstSize = appConfig.RateLimit.Defaults.BurstSize
		}

		operations[opName] = &OperationConfig{
			Enabled:           true,
			RequestsPerMinute: int64(rpm),
			RequestsPerHour:   int64(rph),
			RequestsPerDay:    int64(rpd),
			BurstSize:         int64(burstSize),
			WindowDuration:    time.Minute,
			Limit:             int64(rpm),
		}
	}

	return &Config{
		Enabled:    true,
		Backend:    backendConfig,
		Strategy:   strategyConfig,
		KeyBy:      appConfig.RateLimit.KeyBy,
		HeaderName: appConfig.RateLimit.HeaderName,
		Operations: operations,
		Defaults:   defaults,
	}, nil
}

// GetOperationConfig returns the rate limiting configuration for a specific operation
func (c *Config) GetOperationConfig(operation string) (*OperationConfig, error) {
	if !c.Enabled {
		return nil, fmt.Errorf("rate limiting is disabled")
	}

	opConfig, exists := c.Operations[operation]
	if !exists {
		// Return defaults if operation-specific config doesn't exist
		return c.Defaults, nil
	}

	return opConfig, nil
}

// ValidateConfig validates the rate limiting configuration
func (c *Config) ValidateConfig() error {
	if !c.Enabled {
		return nil
	}

	// Validate backend type
	validBackends := []string{"memory", "redis"}
	backendValid := slices.Contains(validBackends, c.Backend.Type)
	if !backendValid {
		return fmt.Errorf("invalid backend type '%s', must be one of: %v", c.Backend.Type, validBackends)
	}

	// Validate strategy type
	validStrategies := []string{"token_bucket", "sliding_window", "fixed_window"}
	strategyValid := slices.Contains(validStrategies, c.Strategy.Type)
	if !strategyValid {
		return fmt.Errorf("invalid strategy type '%s', must be one of: %v", c.Strategy.Type, validStrategies)
	}

	// Validate key_by
	validKeyBy := []string{"ip", "api_key", "header"}
	keyByValid := slices.Contains(validKeyBy, c.KeyBy)
	if !keyByValid {
		return fmt.Errorf("invalid key_by '%s', must be one of: %v", c.KeyBy, validKeyBy)
	}

	// Validate header name if key_by is "header"
	if c.KeyBy == "header" && c.HeaderName == "" {
		return fmt.Errorf("header_name cannot be empty when key_by is 'header'")
	}

	// Validate Redis configuration if backend is "redis"
	if c.Backend.Type == "redis" {
		if c.Backend.RedisAddress == "" {
			return fmt.Errorf("redis address cannot be empty when using redis backend")
		}
		if c.Backend.RedisPoolSize <= 0 {
			return fmt.Errorf("redis pool_size must be greater than 0")
		}
	}

	return nil
}
