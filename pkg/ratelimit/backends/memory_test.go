//go:build fast

package backends

import (
	"context"
	"testing"
	"time"
)

func TestNewMemoryBackend(t *testing.T) {
	config := BackendConfig{
		Type:            "memory",
		CleanupInterval: 1 * time.Minute,
		MaxKeys:         1000,
	}

	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got: %v", err)
	}

	if backend == nil {
		t.<PERSON>r("Expected backend to be created")
	}

	stats := backend.GetStats()
	if stats.MaxKeys != 1000 {
		t.<PERSON><PERSON>rf("Expected max keys 1000, got: %d", stats.MaxKeys)
	}

	if stats.CleanupInterval != 1*time.Minute {
		t.E<PERSON>rf("Expected cleanup interval 1 minute, got: %v", stats.CleanupInterval)
	}

	// Clean up
	backend.Close()
}

func TestNewMemoryBackend_Defaults(t *testing.T) {
	config := BackendConfig{Type: "memory"}

	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected no error, got: %v", err)
	}

	stats := backend.GetStats()
	if stats.MaxKeys != 10000 {
		t.Errorf("Expected default max keys 10000, got: %d", stats.MaxKeys)
	}

	if stats.CleanupInterval != 5*time.Minute {
		t.Errorf("Expected default cleanup interval 5 minutes, got: %v", stats.CleanupInterval)
	}

	// Clean up
	backend.Close()
}

func TestMemoryBackend_GetSet(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"

	// Test Get on non-existent key
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for non-existent key, got: %v", err)
	}
	if data != nil {
		t.Error("Expected nil data for non-existent key")
	}

	// Test Set
	now := time.Now()
	testData := &BackendData{
		Count:       5,
		WindowStart: now,
		LastRequest: now,
		Tokens:      10.5,
		LastRefill:  now,
		Metadata:    map[string]interface{}{"test": "value"},
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Test Get on existing key
	retrievedData, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if retrievedData == nil {
		t.Fatal("Expected data to be retrieved")
	}

	if retrievedData.Count != 5 {
		t.Errorf("Expected count 5, got: %d", retrievedData.Count)
	}
	if retrievedData.Tokens != 10.5 {
		t.Errorf("Expected tokens 10.5, got: %f", retrievedData.Tokens)
	}
	if retrievedData.Metadata["test"] != "value" {
		t.Errorf("Expected metadata test=value, got: %v", retrievedData.Metadata["test"])
	}
}

func TestMemoryBackend_SetNilData(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	err = backend.Set(ctx, "test-key", nil, time.Minute)
	if err == nil {
		t.Error("Expected error for nil data")
	}

	expectedErr := "data cannot be nil"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestMemoryBackend_Increment(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	window := time.Minute

	// First increment - should create new entry
	count, err := backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error for first increment, got: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1, got: %d", count)
	}

	// Second increment - should increment within window
	count, err = backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error for second increment, got: %v", err)
	}
	if count != 2 {
		t.Errorf("Expected count 2, got: %d", count)
	}

	// Third increment - should increment within window
	count, err = backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error for third increment, got: %v", err)
	}
	if count != 3 {
		t.Errorf("Expected count 3, got: %d", count)
	}
}

func TestMemoryBackend_IncrementWindowReset(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"
	window := 100 * time.Millisecond

	// First increment
	count, err := backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1, got: %d", count)
	}

	// Wait for window to expire
	time.Sleep(150 * time.Millisecond)

	// Increment after window expiry - should reset
	count, err = backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1 after window reset, got: %d", count)
	}
}

func TestMemoryBackend_Delete(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-key"

	// Set some data
	testData := &BackendData{
		Count:       5,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Verify data exists
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if data == nil {
		t.Error("Expected data to exist")
	}

	// Delete the data
	err = backend.Delete(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Delete, got: %v", err)
	}

	// Verify data is gone
	data, err = backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get after delete, got: %v", err)
	}
	if data != nil {
		t.Error("Expected data to be deleted")
	}
}

func TestMemoryBackend_MaxKeys(t *testing.T) {
	config := BackendConfig{
		Type:    "memory",
		MaxKeys: 2, // Very small limit for testing
	}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	testData := &BackendData{
		Count:       1,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	// Add first key - should succeed
	err = backend.Set(ctx, "key1", testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for first key, got: %v", err)
	}

	// Add second key - should succeed
	err = backend.Set(ctx, "key2", testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for second key, got: %v", err)
	}

	// Add third key - should fail due to capacity
	err = backend.Set(ctx, "key3", testData, time.Minute)
	if err == nil {
		t.Error("Expected error for third key due to capacity limit")
	}

	// Update existing key - should succeed
	err = backend.Set(ctx, "key1", testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for updating existing key, got: %v", err)
	}
}

func TestMemoryBackend_IncrementMaxKeys(t *testing.T) {
	config := BackendConfig{
		Type:    "memory",
		MaxKeys: 1, // Very small limit for testing
	}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	window := time.Minute

	// First increment - should succeed
	_, err = backend.Increment(ctx, "key1", window)
	if err != nil {
		t.Errorf("Expected no error for first key, got: %v", err)
	}

	// Second increment on different key - should fail due to capacity
	_, err = backend.Increment(ctx, "key2", window)
	if err == nil {
		t.Error("Expected error for second key due to capacity limit")
	}

	// Increment on existing key - should succeed
	count, err := backend.Increment(ctx, "key1", window)
	if err != nil {
		t.Errorf("Expected no error for existing key, got: %v", err)
	}
	if count != 2 {
		t.Errorf("Expected count 2, got: %d", count)
	}
}

func TestMemoryBackend_Close(t *testing.T) {
	config := BackendConfig{Type: "memory"}
	backend, err := NewMemoryBackend(config)
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}

	// Add some data
	ctx := context.Background()
	testData := &BackendData{
		Count:       1,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}
	backend.Set(ctx, "test-key", testData, time.Minute)

	// Close should not error
	err = backend.Close()
	if err != nil {
		t.Errorf("Expected no error for Close, got: %v", err)
	}

	// Operations after close should not panic (though behavior is undefined)
	// This is mainly to ensure cleanup goroutine stops properly
}
