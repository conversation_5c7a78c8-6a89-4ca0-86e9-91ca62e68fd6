//go:build integration

package backends

import (
	"context"
	"testing"
	"time"
)

// These tests require a running Redis instance
// Run with: go test -tags integration

func getRedisConfig() BackendConfig {
	return BackendConfig{
		Type:          "redis",
		RedisAddress:  "localhost:6379",
		RedisPassword: "",
		RedisDB:       1, // Use DB 1 for testing
		RedisPoolSize: 5,
	}
}

func TestNewRedisBackend(t *testing.T) {
	config := getRedisConfig()

	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	if backend == nil {
		t.Error("Expected backend to be created")
	}

	// Test ping
	ctx := context.Background()
	err = backend.Ping(ctx)
	if err != nil {
		t.Errorf("Expected ping to succeed, got: %v", err)
	}
}

func TestNewRedisBackend_InvalidAddress(t *testing.T) {
	config := BackendConfig{
		Type:         "redis",
		RedisAddress: "invalid:99999",
		RedisDB:      1,
	}

	_, err := NewRedisBackend(config)
	if err == nil {
		t.Error("Expected error for invalid Redis address")
	}
}

func TestNewRedisBackend_EmptyAddress(t *testing.T) {
	config := BackendConfig{
		Type:         "redis",
		RedisAddress: "",
	}

	_, err := NewRedisBackend(config)
	if err == nil {
		t.Error("Expected error for empty Redis address")
	}

	expectedErr := "redis address cannot be empty"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestRedisBackend_GetSet(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-get-set"

	// Clean up before test
	backend.Delete(ctx, key)

	// Test Get on non-existent key
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for non-existent key, got: %v", err)
	}
	if data != nil {
		t.Error("Expected nil data for non-existent key")
	}

	// Test Set
	now := time.Now()
	testData := &BackendData{
		Count:       5,
		WindowStart: now,
		LastRequest: now,
		Tokens:      10.5,
		LastRefill:  now,
		Metadata:    map[string]interface{}{"test": "value", "number": float64(42)},
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Test Get on existing key
	retrievedData, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if retrievedData == nil {
		t.Fatal("Expected data to be retrieved")
	}

	if retrievedData.Count != 5 {
		t.Errorf("Expected count 5, got: %d", retrievedData.Count)
	}
	if retrievedData.Tokens != 10.5 {
		t.Errorf("Expected tokens 10.5, got: %f", retrievedData.Tokens)
	}
	if retrievedData.Metadata["test"] != "value" {
		t.Errorf("Expected metadata test=value, got: %v", retrievedData.Metadata["test"])
	}
	if retrievedData.Metadata["number"] != float64(42) {
		t.Errorf("Expected metadata number=42, got: %v", retrievedData.Metadata["number"])
	}

	// Clean up
	backend.Delete(ctx, key)
}

func TestRedisBackend_SetNilData(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	err = backend.Set(ctx, "test-nil", nil, time.Minute)
	if err == nil {
		t.Error("Expected error for nil data")
	}

	expectedErr := "data cannot be nil"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestRedisBackend_Increment(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-increment"
	window := time.Minute

	// Clean up before test
	backend.Delete(ctx, key)

	// First increment - should create new entry
	count, err := backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error for first increment, got: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1, got: %d", count)
	}

	// Second increment - should increment within window
	count, err = backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error for second increment, got: %v", err)
	}
	if count != 2 {
		t.Errorf("Expected count 2, got: %d", count)
	}

	// Third increment - should increment within window
	count, err = backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error for third increment, got: %v", err)
	}
	if count != 3 {
		t.Errorf("Expected count 3, got: %d", count)
	}

	// Clean up
	backend.Delete(ctx, key)
}

func TestRedisBackend_IncrementWindowReset(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-window-reset"
	window := 2 * time.Second // Short window for testing

	// Clean up before test
	backend.Delete(ctx, key)

	// First increment
	count, err := backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1, got: %d", count)
	}

	// Wait for window to expire
	time.Sleep(3 * time.Second)

	// Increment after window expiry - should reset
	count, err = backend.Increment(ctx, key, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if count != 1 {
		t.Errorf("Expected count 1 after window reset, got: %d", count)
	}

	// Clean up
	backend.Delete(ctx, key)
}

func TestRedisBackend_Delete(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-delete"

	// Set some data
	testData := &BackendData{
		Count:       5,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	err = backend.Set(ctx, key, testData, time.Minute)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Verify data exists
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if data == nil {
		t.Error("Expected data to exist")
	}

	// Delete the data
	err = backend.Delete(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Delete, got: %v", err)
	}

	// Verify data is gone
	data, err = backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get after delete, got: %v", err)
	}
	if data != nil {
		t.Error("Expected data to be deleted")
	}
}

func TestRedisBackend_GetStats(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	stats, err := backend.GetStats(ctx)
	if err != nil {
		t.Errorf("Expected no error for GetStats, got: %v", err)
	}

	if stats == nil {
		t.Error("Expected stats to be returned")
	}

	if stats.Address != config.RedisAddress {
		t.Errorf("Expected address %s, got: %s", config.RedisAddress, stats.Address)
	}

	if stats.DB != config.RedisDB {
		t.Errorf("Expected DB %d, got: %d", config.RedisDB, stats.DB)
	}

	if stats.PoolSize != config.RedisPoolSize {
		t.Errorf("Expected pool size %d, got: %d", config.RedisPoolSize, stats.PoolSize)
	}
}

func TestRedisBackend_ConcurrentIncrement(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-concurrent"
	window := time.Minute

	// Clean up before test
	backend.Delete(ctx, key)

	// Run concurrent increments
	numGoroutines := 10
	incrementsPerGoroutine := 5
	results := make(chan int64, numGoroutines*incrementsPerGoroutine)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			for j := 0; j < incrementsPerGoroutine; j++ {
				count, err := backend.Increment(ctx, key, window)
				if err != nil {
					t.Errorf("Concurrent increment failed: %v", err)
					return
				}
				results <- count
			}
		}()
	}

	// Collect results
	counts := make([]int64, 0, numGoroutines*incrementsPerGoroutine)
	for i := 0; i < numGoroutines*incrementsPerGoroutine; i++ {
		counts = append(counts, <-results)
	}

	// Verify final count
	finalData, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error getting final data, got: %v", err)
	}

	expectedFinalCount := int64(numGoroutines * incrementsPerGoroutine)
	if finalData.Count != expectedFinalCount {
		t.Errorf("Expected final count %d, got: %d", expectedFinalCount, finalData.Count)
	}

	// Clean up
	backend.Delete(ctx, key)
}

func TestRedisBackend_TTL(t *testing.T) {
	config := getRedisConfig()
	backend, err := NewRedisBackend(config)
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-ttl"

	// Clean up before test
	backend.Delete(ctx, key)

	// Set data with short TTL
	testData := &BackendData{
		Count:       1,
		WindowStart: time.Now(),
		LastRequest: time.Now(),
	}

	err = backend.Set(ctx, key, testData, 2*time.Second)
	if err != nil {
		t.Errorf("Expected no error for Set, got: %v", err)
	}

	// Verify data exists
	data, err := backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get, got: %v", err)
	}
	if data == nil {
		t.Error("Expected data to exist")
	}

	// Wait for TTL to expire
	time.Sleep(3 * time.Second)

	// Verify data is gone
	data, err = backend.Get(ctx, key)
	if err != nil {
		t.Errorf("Expected no error for Get after TTL, got: %v", err)
	}
	if data != nil {
		t.Error("Expected data to be expired")
	}
}
