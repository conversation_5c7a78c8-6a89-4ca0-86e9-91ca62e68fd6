package backends

import (
	"context"
	"fmt"
	"maps"
	"sync"
	"time"
)

// MemoryBackend implements the Backend interface using in-memory storage
type MemoryBackend struct {
	data            map[string]*BackendData
	mutex           sync.RWMutex
	cleanupInterval time.Duration
	maxKeys         int
	stopCleanup     chan struct{}
	cleanupDone     chan struct{}
}

// NewMemoryBackend creates a new memory backend
func NewMemoryBackend(config BackendConfig) (*MemoryBackend, error) {
	cleanupInterval := config.CleanupInterval
	if cleanupInterval == 0 {
		cleanupInterval = 5 * time.Minute // Default cleanup interval
	}

	maxKeys := config.MaxKeys
	if maxKeys == 0 {
		maxKeys = 10000 // Default max keys
	}

	backend := &MemoryBackend{
		data:            make(map[string]*BackendData),
		cleanupInterval: cleanupInterval,
		maxKeys:         maxKeys,
		stopCleanup:     make(chan struct{}),
		cleanupDone:     make(chan struct{}),
	}

	// Start cleanup goroutine
	go backend.cleanupLoop()

	return backend, nil
}

// Get retrieves rate limit data for the given key
func (m *MemoryBackend) Get(ctx context.Context, key string) (*BackendData, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	data, exists := m.data[key]
	if !exists {
		return nil, nil // Return nil for non-existent keys
	}

	// Return a copy to avoid race conditions
	return &BackendData{
		Count:       data.Count,
		WindowStart: data.WindowStart,
		LastRequest: data.LastRequest,
		Tokens:      data.Tokens,
		LastRefill:  data.LastRefill,
		Metadata:    copyMetadata(data.Metadata),
	}, nil
}

// Set stores rate limit data for the given key with optional TTL
func (m *MemoryBackend) Set(ctx context.Context, key string, data *BackendData, ttl time.Duration) error {
	if data == nil {
		return fmt.Errorf("data cannot be nil")
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if we're at max capacity and this is a new key
	if _, exists := m.data[key]; !exists && len(m.data) >= m.maxKeys {
		return fmt.Errorf("memory backend at maximum capacity (%d keys)", m.maxKeys)
	}

	// Store a copy to avoid race conditions
	m.data[key] = &BackendData{
		Count:       data.Count,
		WindowStart: data.WindowStart,
		LastRequest: data.LastRequest,
		Tokens:      data.Tokens,
		LastRefill:  data.LastRefill,
		Metadata:    copyMetadata(data.Metadata),
	}

	// TTL is handled by the cleanup goroutine checking LastRequest
	// We don't need to set explicit expiration times

	return nil
}

// Increment atomically increments the counter for the given key within a time window
func (m *MemoryBackend) Increment(ctx context.Context, key string, window time.Duration) (int64, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	data, exists := m.data[key]

	if !exists {
		// Check capacity for new key
		if len(m.data) >= m.maxKeys {
			return 0, fmt.Errorf("memory backend at maximum capacity (%d keys)", m.maxKeys)
		}

		// Create new entry
		m.data[key] = &BackendData{
			Count:       1,
			WindowStart: now,
			LastRequest: now,
			Tokens:      0,
			LastRefill:  now,
			Metadata:    make(map[string]any),
		}
		return 1, nil
	}

	// Check if we need to reset the window
	if now.Sub(data.WindowStart) >= window {
		// Reset the window
		data.Count = 1
		data.WindowStart = now
		data.LastRequest = now
		return 1, nil
	}

	// Increment within the current window
	data.Count++
	data.LastRequest = now

	return data.Count, nil
}

// Delete removes rate limit data for the given key
func (m *MemoryBackend) Delete(ctx context.Context, key string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	delete(m.data, key)
	return nil
}

// Close releases any resources held by the backend
func (m *MemoryBackend) Close() error {
	// Signal cleanup goroutine to stop
	close(m.stopCleanup)

	// Wait for cleanup goroutine to finish
	<-m.cleanupDone

	// Clear data
	m.mutex.Lock()
	m.data = nil
	m.mutex.Unlock()

	return nil
}

// GetStats returns statistics about the memory backend
func (m *MemoryBackend) GetStats() MemoryBackendStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return MemoryBackendStats{
		KeyCount:        len(m.data),
		MaxKeys:         m.maxKeys,
		CleanupInterval: m.cleanupInterval,
	}
}

// MemoryBackendStats holds statistics about the memory backend
type MemoryBackendStats struct {
	KeyCount        int
	MaxKeys         int
	CleanupInterval time.Duration
}

// cleanupLoop runs periodically to clean up expired entries
func (m *MemoryBackend) cleanupLoop() {
	defer close(m.cleanupDone)

	ticker := time.NewTicker(m.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanup()
		case <-m.stopCleanup:
			return
		}
	}
}

// cleanup removes expired entries from the backend
func (m *MemoryBackend) cleanup() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	// Find expired keys (entries not accessed for more than cleanup interval)
	for key, data := range m.data {
		if now.Sub(data.LastRequest) > m.cleanupInterval*2 {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// Remove expired keys
	for _, key := range expiredKeys {
		delete(m.data, key)
	}
}

// copyMetadata creates a deep copy of metadata map
func copyMetadata(metadata map[string]any) map[string]any {
	if metadata == nil {
		return make(map[string]any)
	}

	copy := make(map[string]any)
	maps.Copy(copy, metadata)
	return copy
}
