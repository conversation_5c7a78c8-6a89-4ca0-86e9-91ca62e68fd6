package backends

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisBackend implements the Backend interface using Redis storage
type RedisBackend struct {
	client *redis.Client
	config BackendConfig
}

// NewRedisBackend creates a new Redis backend
func NewRedisBackend(config BackendConfig) (*RedisBackend, error) {
	if config.RedisAddress == "" {
		return nil, fmt.Errorf("redis address cannot be empty")
	}

	if config.RedisPoolSize <= 0 {
		config.RedisPoolSize = 10 // Default pool size
	}

	// Create Redis client
	client := redis.NewClient(&redis.Options{
		Addr:         config.RedisAddress,
		Password:     config.RedisPassword,
		DB:           config.RedisDB,
		PoolSize:     config.RedisPoolSize,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 5 * time.Second,
		DialTimeout:  5 * time.Second,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisBackend{
		client: client,
		config: config,
	}, nil
}

// Get retrieves rate limit data for the given key
func (r *RedisBackend) Get(ctx context.Context, key string) (*BackendData, error) {
	result, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // Key doesn't exist
		}
		return nil, fmt.Errorf("failed to get key from Redis: %w", err)
	}

	var data BackendData
	if err := json.Unmarshal([]byte(result), &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal data from Redis: %w", err)
	}

	return &data, nil
}

// Set stores rate limit data for the given key with optional TTL
func (r *RedisBackend) Set(ctx context.Context, key string, data *BackendData, ttl time.Duration) error {
	if data == nil {
		return fmt.Errorf("data cannot be nil")
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data for Redis: %w", err)
	}

	if ttl > 0 {
		err = r.client.Set(ctx, key, jsonData, ttl).Err()
	} else {
		err = r.client.Set(ctx, key, jsonData, 0).Err()
	}

	if err != nil {
		return fmt.Errorf("failed to set key in Redis: %w", err)
	}

	return nil
}

// Increment atomically increments the counter for the given key within a time window
func (r *RedisBackend) Increment(ctx context.Context, key string, window time.Duration) (int64, error) {
	// Use Lua script for atomic increment with window management
	luaScript := `
		local key = KEYS[1]
		local window_seconds = tonumber(ARGV[1])
		local now = tonumber(ARGV[2])
		
		-- Get current data
		local current = redis.call('GET', key)
		local data = {}
		
		if current then
			data = cjson.decode(current)
		else
			data = {
				count = 0,
				window_start = now,
				last_request = now,
				tokens = 0,
				last_refill = now,
				metadata = {}
			}
		end
		
		-- Check if window has expired
		if (now - data.window_start) >= window_seconds then
			-- Reset window
			data.count = 1
			data.window_start = now
			data.last_request = now
		else
			-- Increment within current window
			data.count = data.count + 1
			data.last_request = now
		end
		
		-- Store updated data with TTL
		local ttl = window_seconds * 2  -- TTL is 2x window size for safety
		redis.call('SET', key, cjson.encode(data), 'EX', ttl)
		
		return data.count
	`

	now := time.Now().Unix()
	windowSeconds := int64(window.Seconds())

	result, err := r.client.Eval(ctx, luaScript, []string{key}, windowSeconds, now).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to increment key in Redis: %w", err)
	}

	count, ok := result.(int64)
	if !ok {
		return 0, fmt.Errorf("unexpected result type from Redis increment: %T", result)
	}

	return count, nil
}

// Delete removes rate limit data for the given key
func (r *RedisBackend) Delete(ctx context.Context, key string) error {
	err := r.client.Del(ctx, key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete key from Redis: %w", err)
	}

	return nil
}

// Close releases any resources held by the backend
func (r *RedisBackend) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetStats returns statistics about the Redis backend
func (r *RedisBackend) GetStats(ctx context.Context) (*RedisBackendStats, error) {
	info, err := r.client.Info(ctx, "memory", "keyspace").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis info: %w", err)
	}

	// Parse basic stats from Redis INFO command
	stats := &RedisBackendStats{
		Address:  r.config.RedisAddress,
		DB:       r.config.RedisDB,
		PoolSize: r.config.RedisPoolSize,
		Info:     info,
	}

	// Get connection pool stats
	poolStats := r.client.PoolStats()
	stats.PoolStats = RedisPoolStats{
		Hits:       poolStats.Hits,
		Misses:     poolStats.Misses,
		Timeouts:   poolStats.Timeouts,
		TotalConns: poolStats.TotalConns,
		IdleConns:  poolStats.IdleConns,
		StaleConns: poolStats.StaleConns,
	}

	return stats, nil
}

// RedisBackendStats holds statistics about the Redis backend
type RedisBackendStats struct {
	Address   string
	DB        int
	PoolSize  int
	Info      string
	PoolStats RedisPoolStats
}

// RedisPoolStats holds Redis connection pool statistics
type RedisPoolStats struct {
	Hits       uint32
	Misses     uint32
	Timeouts   uint32
	TotalConns uint32
	IdleConns  uint32
	StaleConns uint32
}

// Ping tests the Redis connection
func (r *RedisBackend) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// FlushDB clears all keys in the current database (use with caution!)
func (r *RedisBackend) FlushDB(ctx context.Context) error {
	return r.client.FlushDB(ctx).Err()
}

// Keys returns all keys matching the pattern (use with caution in production!)
func (r *RedisBackend) Keys(ctx context.Context, pattern string) ([]string, error) {
	return r.client.Keys(ctx, pattern).Result()
}

// IncrementWithLua performs increment using a custom Lua script
func (r *RedisBackend) IncrementWithLua(ctx context.Context, key string, window time.Duration, customScript string) (int64, error) {
	now := time.Now().Unix()
	windowSeconds := int64(window.Seconds())

	result, err := r.client.Eval(ctx, customScript, []string{key}, windowSeconds, now).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to execute custom Lua script: %w", err)
	}

	// Try to convert result to int64
	switch v := result.(type) {
	case int64:
		return v, nil
	case string:
		if count, err := strconv.ParseInt(v, 10, 64); err == nil {
			return count, nil
		}
	}

	return 0, fmt.Errorf("unexpected result type from custom Lua script: %T", result)
}
