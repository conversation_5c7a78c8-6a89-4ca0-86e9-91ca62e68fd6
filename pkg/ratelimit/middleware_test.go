//go:build fast

package ratelimit

import (
	"encoding/json"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

func init() {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)
}

func TestGinMiddleware_Disabled(t *testing.T) {
	// Create disabled rate limiter
	cfg := &Config{Enabled: false}
	log := logger.NewDefault()
	limiter := &Limiter{config: cfg, logger: log}

	// Create middleware
	middleware := GinMiddleware(limiter, cfg, log)

	// Create test router
	router := gin.New()
	router.Use(middleware)
	router.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "success"})
	})

	// Make request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should pass through without rate limiting
	if w.Code != 200 {
		t.Errorf("Expected status 200, got: %d", w.Code)
	}

	// Should not have rate limit headers
	if w.Header().Get("X-RateLimit-Limit") != "" {
		t.Error("Should not have rate limit headers when disabled")
	}
}

func TestGinMiddleware_Allow(t *testing.T) {
	// Create app config with token bucket strategy
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
			},
		},
	}

	// Create rate limiter
	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	// Resolve config
	rateLimitConfig, err := ResolveConfig(appConfig)
	if err != nil {
		t.Fatalf("Failed to resolve config: %v", err)
	}

	// Create middleware
	middleware := GinMiddleware(limiter, rateLimitConfig, log)

	// Create test router
	router := gin.New()
	router.Use(middleware)
	router.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "success"})
	})

	// Make request
	req := httptest.NewRequest("GET", "/test", nil)
	req.RemoteAddr = "***********:12345"
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should be allowed
	if w.Code != 200 {
		t.Errorf("Expected status 200, got: %d", w.Code)
	}

	// Should have rate limit headers
	if w.Header().Get("X-RateLimit-Limit") == "" {
		t.Error("Should have X-RateLimit-Limit header")
	}

	if w.Header().Get("X-RateLimit-Remaining") == "" {
		t.Error("Should have X-RateLimit-Remaining header")
	}

	// Check header values
	limit := w.Header().Get("X-RateLimit-Limit")
	if limit != "10" {
		t.Errorf("Expected limit 10, got: %s", limit)
	}

	remaining := w.Header().Get("X-RateLimit-Remaining")
	if remaining != "9" { // Started with 10, consumed 1
		t.Errorf("Expected remaining 9, got: %s", remaining)
	}
}

func TestGinMiddleware_RateLimitExceeded(t *testing.T) {
	// Create app config with very low limits
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 2,
				RequestsPerHour:   10,
				RequestsPerDay:    100,
				BurstSize:         2,
			},
		},
	}

	// Create rate limiter
	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	// Resolve config
	rateLimitConfig, err := ResolveConfig(appConfig)
	if err != nil {
		t.Fatalf("Failed to resolve config: %v", err)
	}

	// Create middleware
	middleware := GinMiddleware(limiter, rateLimitConfig, log)

	// Create test router
	router := gin.New()
	router.Use(middleware)
	router.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "success"})
	})

	// Make requests to exhaust limit
	for i := 0; i < 2; i++ {
		req := httptest.NewRequest("GET", "/test", nil)
		req.RemoteAddr = "***********:12345"
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		if w.Code != 200 {
			t.Errorf("Request %d should be allowed, got status: %d", i+1, w.Code)
		}
	}

	// Next request should be rate limited
	req := httptest.NewRequest("GET", "/test", nil)
	req.RemoteAddr = "***********:12345"
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should be rate limited
	if w.Code != 429 {
		t.Errorf("Expected status 429, got: %d", w.Code)
	}

	// Should have Retry-After header
	if w.Header().Get("Retry-After") == "" {
		t.Error("Should have Retry-After header when rate limited")
	}

	// Check response body
	var response map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to parse response: %v", err)
	}

	if response["error"] != "Rate limit exceeded" {
		t.Errorf("Expected error message, got: %v", response["error"])
	}
}

func TestGinMiddleware_IPExtraction(t *testing.T) {
	tests := []struct {
		name          string
		remoteAddr    string
		xForwardedFor string
		xRealIP       string
		expectedIP    string
	}{
		{
			name:       "direct connection",
			remoteAddr: "***********:12345",
			expectedIP: "***********",
		},
		{
			name:          "x-forwarded-for single",
			remoteAddr:    "********:12345",
			xForwardedFor: "***********",
			expectedIP:    "***********",
		},
		{
			name:          "x-forwarded-for multiple",
			remoteAddr:    "********:12345",
			xForwardedFor: "***********, ********, ********",
			expectedIP:    "***********",
		},
		{
			name:       "x-real-ip",
			remoteAddr: "********:12345",
			xRealIP:    "***********",
			expectedIP: "***********",
		},
		{
			name:          "x-forwarded-for takes precedence",
			remoteAddr:    "********:12345",
			xForwardedFor: "***********",
			xRealIP:       "***********",
			expectedIP:    "***********",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create simple config
			cfg := &Config{
				Enabled: true,
				KeyBy:   "ip",
			}
			log := logger.NewDefault()

			middleware := &ginMiddleware{
				config: cfg,
				logger: log,
			}

			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/test", nil)
			c.Request.RemoteAddr = tt.remoteAddr

			if tt.xForwardedFor != "" {
				c.Request.Header.Set("X-Forwarded-For", tt.xForwardedFor)
			}
			if tt.xRealIP != "" {
				c.Request.Header.Set("X-Real-IP", tt.xRealIP)
			}

			// Extract IP
			ip := middleware.extractIPAddress(c)

			if ip != tt.expectedIP {
				t.Errorf("Expected IP %s, got: %s", tt.expectedIP, ip)
			}
		})
	}
}

func TestGinMiddleware_APIKeyExtraction(t *testing.T) {
	tests := []struct {
		name          string
		authorization string
		expectedKey   string
	}{
		{
			name:          "bearer token",
			authorization: "Bearer sk-test-key-123",
			expectedKey:   "sk-test-key-123",
		},
		{
			name:          "api-key format",
			authorization: "Api-Key sk-test-key-456",
			expectedKey:   "sk-test-key-456",
		},
		{
			name:          "direct key",
			authorization: "sk-test-key-789",
			expectedKey:   "sk-test-key-789",
		},
		{
			name:          "empty header",
			authorization: "",
			expectedKey:   "",
		},
		{
			name:          "bearer with spaces",
			authorization: "Bearer  sk-test-key-with-spaces  ",
			expectedKey:   "sk-test-key-with-spaces",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &Config{
				Enabled: true,
				KeyBy:   "api_key",
			}
			log := logger.NewDefault()

			middleware := &ginMiddleware{
				config: cfg,
				logger: log,
			}

			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest("GET", "/test", nil)

			if tt.authorization != "" {
				c.Request.Header.Set("Authorization", tt.authorization)
			}

			// Extract API key
			key := middleware.extractAPIKey(c)

			if key != tt.expectedKey {
				t.Errorf("Expected key '%s', got: '%s'", tt.expectedKey, key)
			}
		})
	}
}

func TestGinMiddleware_OperationExtraction(t *testing.T) {
	tests := []struct {
		name              string
		path              string
		expectedOperation string
	}{
		{
			name:              "tailor endpoint",
			path:              "/api/v1/tailor",
			expectedOperation: "tailor",
		},
		{
			name:              "evaluate endpoint",
			path:              "/api/v1/evaluate",
			expectedOperation: "evaluate",
		},
		{
			name:              "analyze endpoint",
			path:              "/api/v1/analyze",
			expectedOperation: "analyze",
		},
		{
			name:              "legacy tailor",
			path:              "/tailor",
			expectedOperation: "tailor",
		},
		{
			name:              "unknown endpoint",
			path:              "/api/v1/unknown",
			expectedOperation: "",
		},
		{
			name:              "health endpoint",
			path:              "/health",
			expectedOperation: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &Config{Enabled: true}
			log := logger.NewDefault()

			middleware := &ginMiddleware{
				config: cfg,
				logger: log,
			}

			// Create test router and context to properly simulate Gin's behavior
			router := gin.New()
			w := httptest.NewRecorder()

			// Add a route that matches the test path
			router.GET(tt.path, func(c *gin.Context) {
				// This handler won't be called, we just need the route registration
			})

			// Create request and get context from router
			req := httptest.NewRequest("GET", tt.path, nil)
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// Manually set the full path to simulate what Gin sets
			c.Set("gin.route", tt.path)

			// Extract operation
			operation := middleware.extractOperation(c)

			if operation != tt.expectedOperation {
				t.Errorf("Expected operation '%s', got: '%s'", tt.expectedOperation, operation)
			}
		})
	}
}

func TestGinMiddleware_CustomHeader(t *testing.T) {
	cfg := &Config{
		Enabled:    true,
		KeyBy:      "header",
		HeaderName: "X-Client-ID",
	}
	log := logger.NewDefault()

	middleware := &ginMiddleware{
		config: cfg,
		logger: log,
	}

	// Create test context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/test", nil)
	c.Request.Header.Set("X-Client-ID", "client-123")

	// Extract custom header
	clientID := middleware.extractCustomHeader(c)

	if clientID != "client-123" {
		t.Errorf("Expected client ID 'client-123', got: '%s'", clientID)
	}
}

func TestGinMiddleware_Headers(t *testing.T) {
	// Create app config
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
			},
		},
	}

	// Create rate limiter
	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	// Resolve config
	rateLimitConfig, err := ResolveConfig(appConfig)
	if err != nil {
		t.Fatalf("Failed to resolve config: %v", err)
	}

	// Create middleware
	middleware := GinMiddleware(limiter, rateLimitConfig, log)

	// Create test router
	router := gin.New()
	router.Use(middleware)
	router.GET("/api/v1/tailor", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "success"})
	})

	// Make request
	req := httptest.NewRequest("GET", "/api/v1/tailor", nil)
	req.RemoteAddr = "***********:12345"
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check all expected headers
	expectedHeaders := []string{
		"X-RateLimit-Limit",
		"X-RateLimit-Remaining",
		"X-RateLimit-Operation",
	}

	for _, header := range expectedHeaders {
		if w.Header().Get(header) == "" {
			t.Errorf("Missing header: %s", header)
		}
	}

	// Check specific header values
	if w.Header().Get("X-RateLimit-Operation") != "tailor" {
		t.Errorf("Expected operation 'tailor', got: %s", w.Header().Get("X-RateLimit-Operation"))
	}

	// Parse and validate limit
	limitStr := w.Header().Get("X-RateLimit-Limit")
	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil {
		t.Errorf("Invalid limit header: %s", limitStr)
	}
	if limit != 10 {
		t.Errorf("Expected limit 10, got: %d", limit)
	}
}
