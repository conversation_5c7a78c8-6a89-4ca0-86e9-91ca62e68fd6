package ratelimit

import (
	"context"
	"fmt"
	"time"

	"resumatter/pkg/logger"
	"resumatter/pkg/ratelimit/backends"
	"resumatter/pkg/ratelimit/strategies"
)

// Limiter implements the RateLimiter interface
type Limiter struct {
	config   *Config
	backend  backends.Backend
	strategy strategies.Strategy
	logger   logger.Logger
}

// New creates a new rate limiter instance
func New(config *Config, logger logger.Logger) (*Limiter, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	if !config.Enabled {
		return &Limiter{
			config: config,
			logger: logger,
		}, nil
	}

	// Validate configuration
	if err := config.ValidateConfig(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	// Create backend
	backend, err := backends.NewBackend(config.Backend)
	if err != nil {
		return nil, fmt.Errorf("failed to create backend: %w", err)
	}

	// Create strategy
	strategy, err := strategies.NewStrategy(config.Strategy)
	if err != nil {
		backend.Close() // Clean up backend if strategy creation fails
		return nil, fmt.Errorf("failed to create strategy: %w", err)
	}

	limiter := &Limiter{
		config:   config,
		backend:  backend,
		strategy: strategy,
		logger:   logger,
	}

	logger.Info(context.Background(), "Rate limiter initialized")

	return limiter, nil
}

// Allow checks if a request should be allowed for the given key and operation
func (l *Limiter) Allow(ctx context.Context, key string, operation string) (*Result, error) {
	if !l.config.Enabled {
		// Rate limiting is disabled, allow all requests
		return &Result{
			Allowed:       true,
			Remaining:     -1, // Unlimited
			ResetTime:     time.Time{},
			RetryAfter:    0,
			TotalRequests: 0,
			Limit:         -1, // Unlimited
		}, nil
	}

	// Get operation configuration
	opConfig, err := l.config.GetOperationConfig(operation)
	if err != nil {
		l.logger.ErrorWithErr(ctx, "Failed to get operation config", err)
		// Fail open - allow the request
		return &Result{Allowed: true}, nil
	}

	if !opConfig.Enabled {
		// Rate limiting is disabled for this operation
		return &Result{
			Allowed:       true,
			Remaining:     -1,
			ResetTime:     time.Time{},
			RetryAfter:    0,
			TotalRequests: 0,
			Limit:         -1,
		}, nil
	}

	// Create rate limit key
	rateLimitKey := l.createKey(key, operation)

	// Check rate limit using strategy
	strategyResult, err := l.strategy.Allow(ctx, l.backend, rateLimitKey, opConfig.Limit, opConfig.WindowDuration)
	if err != nil {
		l.logger.ErrorWithErr(ctx, "Rate limit check failed", err)
		// Fail open - allow the request
		return &Result{Allowed: true}, nil
	}

	// Convert strategy result to rate limiter result
	result := &Result{
		Allowed:       strategyResult.Allowed,
		Remaining:     strategyResult.Remaining,
		ResetTime:     strategyResult.ResetTime,
		RetryAfter:    strategyResult.RetryAfter,
		TotalRequests: strategyResult.TotalRequests,
		Limit:         opConfig.Limit,
	}

	// Log rate limit decision
	if !result.Allowed {
		l.logger.Warn(ctx, "Rate limit exceeded")
	} else {
		l.logger.Debug(ctx, "Rate limit check passed")
	}

	return result, nil
}

// Reset clears the rate limit data for the given key and operation
func (l *Limiter) Reset(ctx context.Context, key string, operation string) error {
	if !l.config.Enabled {
		return nil
	}

	rateLimitKey := l.createKey(key, operation)

	err := l.strategy.Reset(ctx, l.backend, rateLimitKey)
	if err != nil {
		l.logger.ErrorWithErr(ctx, "Failed to reset rate limit", err)
		return fmt.Errorf("failed to reset rate limit: %w", err)
	}

	l.logger.Info(ctx, "Rate limit reset")

	return nil
}

// GetStatus returns the current rate limit status for the given key and operation
func (l *Limiter) GetStatus(ctx context.Context, key string, operation string) (*Status, error) {
	if !l.config.Enabled {
		return &Status{
			Limit:          -1,
			Remaining:      -1,
			ResetTime:      time.Time{},
			WindowStart:    time.Time{},
			TotalRequests:  0,
			WindowDuration: 0,
		}, nil
	}

	// Get operation configuration
	opConfig, err := l.config.GetOperationConfig(operation)
	if err != nil {
		return nil, fmt.Errorf("failed to get operation config: %w", err)
	}

	if !opConfig.Enabled {
		return &Status{
			Limit:          -1,
			Remaining:      -1,
			ResetTime:      time.Time{},
			WindowStart:    time.Time{},
			TotalRequests:  0,
			WindowDuration: 0,
		}, nil
	}

	rateLimitKey := l.createKey(key, operation)

	strategyStatus, err := l.strategy.GetStatus(ctx, l.backend, rateLimitKey, opConfig.Limit, opConfig.WindowDuration)
	if err != nil {
		return nil, fmt.Errorf("failed to get rate limit status: %w", err)
	}

	return &Status{
		Limit:          strategyStatus.Limit,
		Remaining:      strategyStatus.Remaining,
		ResetTime:      strategyStatus.ResetTime,
		WindowStart:    strategyStatus.WindowStart,
		TotalRequests:  strategyStatus.TotalRequests,
		WindowDuration: strategyStatus.WindowDuration,
	}, nil
}

// Close releases any resources held by the rate limiter
func (l *Limiter) Close() error {
	if l.backend != nil {
		if err := l.backend.Close(); err != nil {
			l.logger.ErrorWithErr(context.Background(), "Failed to close rate limiter backend", err)
			return fmt.Errorf("failed to close backend: %w", err)
		}
	}

	l.logger.Info(context.Background(), "Rate limiter closed")
	return nil
}

// createKey creates a rate limit key from the client key and operation
func (l *Limiter) createKey(key, operation string) string {
	return fmt.Sprintf("ratelimit:%s:%s", operation, key)
}
