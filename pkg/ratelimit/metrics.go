package ratelimit

import (
	"context"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"resumatter/pkg/logger"
)

// Metrics holds Prometheus metrics for rate limiting
type Metrics struct {
	requestsTotal     *prometheus.CounterVec
	requestsBlocked   *prometheus.CounterVec
	limitExceeded     *prometheus.CounterVec
	backendErrors     *prometheus.CounterVec
	operationDuration *prometheus.HistogramVec
	enabled           bool
	logger            logger.Logger
}

// NewMetrics creates a new metrics instance
func NewMetrics(logger logger.Logger) *Metrics {
	return &Metrics{
		requestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "resumatter_rate_limit_requests_total",
				Help: "Total number of rate limit checks",
			},
			[]string{"operation", "client_type", "allowed"},
		),
		requestsBlocked: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "resumatter_rate_limit_requests_blocked_total",
				Help: "Total number of requests blocked by rate limiting",
			},
			[]string{"operation", "client_type"},
		),
		limitExceeded: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "resumatter_rate_limit_exceeded_total",
				Help: "Total number of rate limit violations",
			},
			[]string{"operation", "client_type"},
		),
		backendErrors: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "resumatter_rate_limit_backend_errors_total",
				Help: "Total number of rate limit backend errors",
			},
			[]string{"backend_type", "operation"},
		),
		operationDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "resumatter_rate_limit_operation_duration_seconds",
				Help:    "Duration of rate limit operations",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation", "backend_type", "strategy_type"},
		),
		enabled: true,
		logger:  logger,
	}
}

// Register registers the metrics with the provided Prometheus registerer
func (m *Metrics) Register(registerer prometheus.Registerer) error {
	if !m.enabled {
		return nil
	}

	collectors := []prometheus.Collector{
		m.requestsTotal,
		m.requestsBlocked,
		m.limitExceeded,
		m.backendErrors,
		m.operationDuration,
	}

	for _, collector := range collectors {
		if err := registerer.Register(collector); err != nil {
			// If already registered, ignore the error
			if _, ok := err.(prometheus.AlreadyRegisteredError); !ok {
				return err
			}
		}
	}

	m.logger.Info(context.Background(), "Rate limit metrics registered")
	return nil
}

// RecordRequest records a rate limit request
func (m *Metrics) RecordRequest(operation, clientType string, allowed bool) {
	if !m.enabled {
		return
	}

	labels := prometheus.Labels{
		"operation":   operation,
		"client_type": clientType,
		"allowed":     boolToString(allowed),
	}

	m.requestsTotal.With(labels).Inc()

	if !allowed {
		blockedLabels := prometheus.Labels{
			"operation":   operation,
			"client_type": clientType,
		}
		m.requestsBlocked.With(blockedLabels).Inc()
	}
}

// RecordLimitExceeded records a rate limit violation
func (m *Metrics) RecordLimitExceeded(operation, clientType string) {
	if !m.enabled {
		return
	}

	labels := prometheus.Labels{
		"operation":   operation,
		"client_type": clientType,
	}

	m.limitExceeded.With(labels).Inc()
}

// RecordBackendError records a backend error
func (m *Metrics) RecordBackendError(backendType, operation string) {
	if !m.enabled {
		return
	}

	labels := prometheus.Labels{
		"backend_type": backendType,
		"operation":    operation,
	}

	m.backendErrors.With(labels).Inc()
}

// RecordOperationDuration records the duration of a rate limit operation
func (m *Metrics) RecordOperationDuration(operation, backendType, strategyType string, duration time.Duration) {
	if !m.enabled {
		return
	}

	labels := prometheus.Labels{
		"operation":     operation,
		"backend_type":  backendType,
		"strategy_type": strategyType,
	}

	m.operationDuration.With(labels).Observe(duration.Seconds())
}

// Disable disables metrics collection
func (m *Metrics) Disable() {
	m.enabled = false
}

// Enable enables metrics collection
func (m *Metrics) Enable() {
	m.enabled = true
}

// IsEnabled returns whether metrics collection is enabled
func (m *Metrics) IsEnabled() bool {
	return m.enabled
}

// boolToString converts a boolean to string for Prometheus labels
func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}
