//go:build fast

package ratelimit

import (
	"context"
	"testing"
	"time"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

func TestNew_DisabledConfig(t *testing.T) {
	cfg := &Config{Enabled: false}
	log := logger.NewDefault()

	limiter, err := New(cfg, log)
	if err != nil {
		t.<PERSON>("Expected no error for disabled config, got: %v", err)
	}

	if limiter == nil {
		t.Error("Expected limiter to be created even when disabled")
	}

	if limiter.config.Enabled {
		t.Error("Expected limiter to be disabled")
	}
}

func TestNew_NilConfig(t *testing.T) {
	log := logger.NewDefault()

	_, err := New(nil, log)
	if err == nil {
		t.Error("Expected error for nil config")
	}

	expectedErr := "config cannot be nil"
	if err.<PERSON><PERSON><PERSON>() != expectedErr {
		t.<PERSON><PERSON><PERSON>("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestLimiter_Allow_Disabled(t *testing.T) {
	cfg := &Config{Enabled: false}
	log := logger.NewDefault()

	limiter, err := New(cfg, log)
	if err != nil {
		t.Fatalf("Failed to create limiter: %v", err)
	}

	ctx := context.Background()
	result, err := limiter.Allow(ctx, "test-key", "test-operation")
	if err != nil {
		t.Errorf("Expected no error for disabled limiter, got: %v", err)
	}

	if !result.Allowed {
		t.Error("Expected request to be allowed when rate limiting is disabled")
	}

	if result.Remaining != -1 {
		t.Errorf("Expected unlimited remaining (-1), got: %d", result.Remaining)
	}

	if result.Limit != -1 {
		t.Errorf("Expected unlimited limit (-1), got: %d", result.Limit)
	}
}

func TestLimiter_GetStatus_Disabled(t *testing.T) {
	cfg := &Config{Enabled: false}
	log := logger.NewDefault()

	limiter, err := New(cfg, log)
	if err != nil {
		t.Fatalf("Failed to create limiter: %v", err)
	}

	ctx := context.Background()
	status, err := limiter.GetStatus(ctx, "test-key", "test-operation")
	if err != nil {
		t.Errorf("Expected no error for disabled limiter, got: %v", err)
	}

	if status.Limit != -1 {
		t.Errorf("Expected unlimited limit (-1), got: %d", status.Limit)
	}

	if status.Remaining != -1 {
		t.Errorf("Expected unlimited remaining (-1), got: %d", status.Remaining)
	}
}

func TestLimiter_Reset_Disabled(t *testing.T) {
	cfg := &Config{Enabled: false}
	log := logger.NewDefault()

	limiter, err := New(cfg, log)
	if err != nil {
		t.Fatalf("Failed to create limiter: %v", err)
	}

	ctx := context.Background()
	err = limiter.Reset(ctx, "test-key", "test-operation")
	if err != nil {
		t.Errorf("Expected no error for disabled limiter, got: %v", err)
	}
}

func TestLimiter_Close_Disabled(t *testing.T) {
	cfg := &Config{Enabled: false}
	log := logger.NewDefault()

	limiter, err := New(cfg, log)
	if err != nil {
		t.Fatalf("Failed to create limiter: %v", err)
	}

	err = limiter.Close()
	if err != nil {
		t.Errorf("Expected no error for disabled limiter, got: %v", err)
	}
}

func TestResolveConfig_Disabled(t *testing.T) {
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled: false,
		},
	}

	cfg, err := ResolveConfig(appConfig)
	if err != nil {
		t.Errorf("Expected no error for disabled config, got: %v", err)
	}

	if cfg.Enabled {
		t.Error("Expected resolved config to be disabled")
	}
}

func TestResolveConfig_Enabled(t *testing.T) {
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 60,
				RequestsPerHour:   1000,
				RequestsPerDay:    10000,
				BurstSize:         10,
			},
			Operations: map[string]config.OperationRateLimit{
				"tailor": {
					Enabled:           true,
					RequestsPerMinute: 10,
					RequestsPerHour:   100,
					RequestsPerDay:    1000,
					BurstSize:         5,
				},
			},
			Redis: config.RedisConfig{
				Address:  "localhost:6379",
				Password: "",
				DB:       0,
				PoolSize: 10,
			},
		},
	}

	cfg, err := ResolveConfig(appConfig)
	if err != nil {
		t.Errorf("Expected no error for valid config, got: %v", err)
	}

	if !cfg.Enabled {
		t.Error("Expected resolved config to be enabled")
	}

	if cfg.Backend.Type != "memory" {
		t.Errorf("Expected backend type 'memory', got: %s", cfg.Backend.Type)
	}

	if cfg.Strategy.Type != "token_bucket" {
		t.Errorf("Expected strategy type 'token_bucket', got: %s", cfg.Strategy.Type)
	}

	if cfg.KeyBy != "ip" {
		t.Errorf("Expected key_by 'ip', got: %s", cfg.KeyBy)
	}

	// Check defaults
	if cfg.Defaults.RequestsPerMinute != 60 {
		t.Errorf("Expected default requests per minute 60, got: %d", cfg.Defaults.RequestsPerMinute)
	}

	// Check operation config
	tailorConfig, exists := cfg.Operations["tailor"]
	if !exists {
		t.Error("Expected tailor operation config to exist")
	} else {
		if tailorConfig.RequestsPerMinute != 10 {
			t.Errorf("Expected tailor requests per minute 10, got: %d", tailorConfig.RequestsPerMinute)
		}
		if tailorConfig.Limit != 10 {
			t.Errorf("Expected tailor limit 10, got: %d", tailorConfig.Limit)
		}
		if tailorConfig.WindowDuration != time.Minute {
			t.Errorf("Expected tailor window duration 1 minute, got: %v", tailorConfig.WindowDuration)
		}
	}
}

func TestConfig_GetOperationConfig(t *testing.T) {
	cfg := &Config{
		Enabled: true,
		Defaults: &OperationConfig{
			Enabled:           true,
			RequestsPerMinute: 60,
			RequestsPerHour:   1000,
			RequestsPerDay:    10000,
			BurstSize:         10,
			WindowDuration:    time.Minute,
			Limit:             60,
		},
		Operations: map[string]*OperationConfig{
			"tailor": {
				Enabled:           true,
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
				WindowDuration:    time.Minute,
				Limit:             10,
			},
		},
	}

	// Test existing operation
	opConfig, err := cfg.GetOperationConfig("tailor")
	if err != nil {
		t.Errorf("Expected no error for existing operation, got: %v", err)
	}
	if opConfig.RequestsPerMinute != 10 {
		t.Errorf("Expected 10 requests per minute, got: %d", opConfig.RequestsPerMinute)
	}

	// Test non-existing operation (should return defaults)
	opConfig, err = cfg.GetOperationConfig("nonexistent")
	if err != nil {
		t.Errorf("Expected no error for non-existing operation, got: %v", err)
	}
	if opConfig.RequestsPerMinute != 60 {
		t.Errorf("Expected default 60 requests per minute, got: %d", opConfig.RequestsPerMinute)
	}

	// Test with rate limiting disabled
	cfg.Enabled = false
	_, err = cfg.GetOperationConfig("tailor")
	if err == nil {
		t.Error("Expected error when rate limiting is disabled")
	}
}

func TestNewFromConfig(t *testing.T) {
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled: false,
		},
	}
	log := logger.NewDefault()

	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if limiter == nil {
		t.Error("Expected limiter to be created")
	}

	if limiter.config.Enabled {
		t.Error("Expected limiter to be disabled")
	}
}

func TestNewFromConfig_NilConfig(t *testing.T) {
	log := logger.NewDefault()

	_, err := NewFromConfig(nil, log)
	if err == nil {
		t.Error("Expected error for nil config")
	}
}

func TestNewFromConfig_NilLogger(t *testing.T) {
	appConfig := &config.Config{}

	_, err := NewFromConfig(appConfig, nil)
	if err == nil {
		t.Error("Expected error for nil logger")
	}
}

func TestNewDisabled(t *testing.T) {
	log := logger.NewDefault()

	limiter := NewDisabled(log)
	if limiter == nil {
		t.Error("Expected limiter to be created")
	}

	if limiter.config.Enabled {
		t.Error("Expected limiter to be disabled")
	}
}
