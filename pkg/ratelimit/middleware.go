package ratelimit

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"resumatter/pkg/logger"
)

// GinMiddleware creates a Gin middleware function for rate limiting
func GinMiddleware(limiter RateLimiter, config *Config, logger logger.Logger) gin.HandlerFunc {
	middleware := &ginMiddleware{
		limiter: limiter,
		config:  config,
		logger:  logger,
	}
	return middleware.Handler
}

// ginMiddleware holds the middleware state
type ginMiddleware struct {
	limiter RateLimiter
	config  *Config
	logger  logger.Logger
}

// Handler is the actual Gin middleware handler function
func (m *ginMiddleware) Handler(c *gin.Context) {
	// Skip if rate limiting is disabled
	if !m.config.Enabled {
		c.Next()
		return
	}

	ctx := c.Request.Context()

	// Extract client identifier
	clientKey, clientType := m.extractClientKey(c)
	if clientKey == "" {
		m.logger.Warn(ctx, "Failed to extract client key for rate limiting")
		// Fail open - allow the request
		c.Next()
		return
	}

	// Extract operation from route
	operation := m.extractOperation(c)
	if operation == "" {
		m.logger.Debug(ctx, "No operation extracted, using default rate limits")
		operation = "default"
	}

	// Check rate limit
	result, err := m.limiter.Allow(ctx, clientKey, operation)
	if err != nil {
		m.logger.ErrorWithErr(ctx, "Rate limit check failed", err)
		// Fail open - allow the request
		c.Next()
		return
	}

	// Add rate limit headers
	m.addRateLimitHeaders(c, result, operation)

	// Log rate limit decision
	m.logRateLimitDecision(ctx, clientKey, clientType, operation, result)

	// Check if request should be blocked
	if !result.Allowed {
		m.handleRateLimitExceeded(c, result)
		return
	}

	// Request is allowed, continue to next handler
	c.Next()
}

// extractClientKey extracts the client identifier based on configuration
func (m *ginMiddleware) extractClientKey(c *gin.Context) (string, string) {
	switch m.config.KeyBy {
	case "ip":
		return m.extractIPAddress(c), "ip"
	case "api_key":
		return m.extractAPIKey(c), "api_key"
	case "header":
		return m.extractCustomHeader(c), "header"
	default:
		// Fallback to IP
		return m.extractIPAddress(c), "ip"
	}
}

// extractIPAddress extracts the client IP address
func (m *ginMiddleware) extractIPAddress(c *gin.Context) string {
	// Check X-Forwarded-For header first (for load balancers/proxies)
	if xff := c.GetHeader("X-Forwarded-For"); xff != "" {
		// Take the first IP in the list
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if net.ParseIP(ip) != nil {
				return ip
			}
		}
	}

	// Check X-Real-IP header
	if xri := c.GetHeader("X-Real-IP"); xri != "" {
		if net.ParseIP(xri) != nil {
			return xri
		}
	}

	// Fall back to RemoteAddr
	ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
	if err != nil {
		// If SplitHostPort fails, RemoteAddr might not have a port
		ip = c.Request.RemoteAddr
	}

	// Validate IP
	if net.ParseIP(ip) != nil {
		return ip
	}

	// Last resort - return a default
	return "unknown"
}

// extractAPIKey extracts the API key from Authorization header
func (m *ginMiddleware) extractAPIKey(c *gin.Context) string {
	auth := c.GetHeader("Authorization")
	if auth == "" {
		return ""
	}

	// Support multiple formats:
	// - "Bearer <key>"
	// - "Api-Key <key>"
	// - "<key>" (direct)

	if strings.HasPrefix(auth, "Bearer ") {
		return strings.TrimSpace(auth[7:])
	}

	if strings.HasPrefix(auth, "Api-Key ") {
		return strings.TrimSpace(auth[8:])
	}

	// Direct key
	return strings.TrimSpace(auth)
}

// extractCustomHeader extracts the client identifier from a custom header
func (m *ginMiddleware) extractCustomHeader(c *gin.Context) string {
	if m.config.HeaderName == "" {
		return ""
	}

	return c.GetHeader(m.config.HeaderName)
}

// extractOperation extracts the operation name from the request route
func (m *ginMiddleware) extractOperation(c *gin.Context) string {
	// Try to get the matched route path first
	route := c.FullPath()

	// If FullPath is empty, fall back to request URL path
	if route == "" {
		route = c.Request.URL.Path
	}

	// Map common Resumatter routes to operations
	switch {
	case strings.Contains(route, "/tailor"):
		return "tailor"
	case strings.Contains(route, "/evaluate"):
		return "evaluate"
	case strings.Contains(route, "/analyze"):
		return "analyze"
	default:
		// For other routes, try to extract from path segments
		segments := strings.Split(strings.Trim(route, "/"), "/")
		for _, segment := range segments {
			// Look for known operation names
			switch segment {
			case "tailor", "evaluate", "analyze":
				return segment
			}
		}

		// No specific operation found
		return ""
	}
}

// addRateLimitHeaders adds standard rate limiting headers to the response
func (m *ginMiddleware) addRateLimitHeaders(c *gin.Context, result *Result, operation string) {
	// Standard rate limit headers
	c.Header("X-RateLimit-Limit", strconv.FormatInt(result.Limit, 10))
	c.Header("X-RateLimit-Remaining", strconv.FormatInt(result.Remaining, 10))

	// Reset time as Unix timestamp
	if !result.ResetTime.IsZero() {
		c.Header("X-RateLimit-Reset", strconv.FormatInt(result.ResetTime.Unix(), 10))
	}

	// Retry-After header when rate limited (in seconds)
	if !result.Allowed && result.RetryAfter > 0 {
		c.Header("Retry-After", strconv.FormatInt(int64(result.RetryAfter.Seconds()), 10))
	}

	// Custom headers for additional information
	c.Header("X-RateLimit-Operation", operation)

	if result.TotalRequests > 0 {
		c.Header("X-RateLimit-Used", strconv.FormatInt(result.TotalRequests, 10))
	}
}

// handleRateLimitExceeded handles the case when rate limit is exceeded
func (m *ginMiddleware) handleRateLimitExceeded(c *gin.Context, result *Result) {
	// Set appropriate status code
	c.Status(http.StatusTooManyRequests)

	// Create error response
	errorResponse := gin.H{
		"error":   "Rate limit exceeded",
		"message": "Too many requests. Please try again later.",
	}

	// Add retry information if available
	if result.RetryAfter > 0 {
		errorResponse["retry_after_seconds"] = int64(result.RetryAfter.Seconds())

		// Human-readable retry time
		if result.RetryAfter < time.Minute {
			errorResponse["retry_after"] = fmt.Sprintf("%d seconds", int64(result.RetryAfter.Seconds()))
		} else {
			errorResponse["retry_after"] = fmt.Sprintf("%d minutes", int64(result.RetryAfter.Minutes()))
		}
	}

	// Add reset time if available
	if !result.ResetTime.IsZero() {
		errorResponse["reset_time"] = result.ResetTime.Format(time.RFC3339)
	}

	// Add limit information
	errorResponse["limit"] = result.Limit
	errorResponse["remaining"] = result.Remaining

	c.JSON(http.StatusTooManyRequests, errorResponse)
	c.Abort()
}

// logRateLimitDecision logs the rate limiting decision
func (m *ginMiddleware) logRateLimitDecision(ctx context.Context, clientKey, clientType, operation string, result *Result) {
	if result.Allowed {
		m.logger.Debug(ctx, "Rate limit check passed")
	} else {
		m.logger.Warn(ctx, "Rate limit exceeded")
	}
}

// MiddlewareConfig holds configuration for the middleware
type MiddlewareConfig struct {
	// SkipPaths contains paths that should skip rate limiting
	SkipPaths []string

	// SkipMethods contains HTTP methods that should skip rate limiting
	SkipMethods []string

	// CustomKeyExtractor allows custom client key extraction
	CustomKeyExtractor func(*gin.Context) (string, string)

	// CustomOperationExtractor allows custom operation extraction
	CustomOperationExtractor func(*gin.Context) string

	// OnRateLimitExceeded allows custom handling of rate limit exceeded
	OnRateLimitExceeded func(*gin.Context, *Result)

	// IncludeHeaders controls which headers to include
	IncludeHeaders RateLimitHeaders
}

// RateLimitHeaders controls which rate limit headers to include
type RateLimitHeaders struct {
	Limit      bool // X-RateLimit-Limit
	Remaining  bool // X-RateLimit-Remaining
	Reset      bool // X-RateLimit-Reset
	RetryAfter bool // Retry-After
	Operation  bool // X-RateLimit-Operation
	Used       bool // X-RateLimit-Used
}

// DefaultRateLimitHeaders returns the default header configuration
func DefaultRateLimitHeaders() RateLimitHeaders {
	return RateLimitHeaders{
		Limit:      true,
		Remaining:  true,
		Reset:      true,
		RetryAfter: true,
		Operation:  true,
		Used:       true,
	}
}

// GinMiddlewareWithConfig creates a Gin middleware with custom configuration
func GinMiddlewareWithConfig(limiter RateLimiter, config *Config, logger logger.Logger, middlewareConfig MiddlewareConfig) gin.HandlerFunc {
	middleware := &ginMiddleware{
		limiter: limiter,
		config:  config,
		logger:  logger,
	}

	return func(c *gin.Context) {
		// Check if path should be skipped
		if middleware.shouldSkipPath(c.Request.URL.Path, middlewareConfig.SkipPaths) {
			c.Next()
			return
		}

		// Check if method should be skipped
		if middleware.shouldSkipMethod(c.Request.Method, middlewareConfig.SkipMethods) {
			c.Next()
			return
		}

		// Use custom key extractor if provided
		if middlewareConfig.CustomKeyExtractor != nil {
			clientKey, clientType := middlewareConfig.CustomKeyExtractor(c)
			middleware.handleRequestWithKey(c, clientKey, clientType, middlewareConfig)
			return
		}

		// Use default handler
		middleware.Handler(c)
	}
}

// shouldSkipPath checks if the path should skip rate limiting
func (m *ginMiddleware) shouldSkipPath(path string, skipPaths []string) bool {
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// shouldSkipMethod checks if the method should skip rate limiting
func (m *ginMiddleware) shouldSkipMethod(method string, skipMethods []string) bool {
	for _, skipMethod := range skipMethods {
		if strings.EqualFold(method, skipMethod) {
			return true
		}
	}
	return false
}

// handleRequestWithKey handles a request with a specific client key
func (m *ginMiddleware) handleRequestWithKey(c *gin.Context, clientKey, clientType string, middlewareConfig MiddlewareConfig) {
	if !m.config.Enabled {
		c.Next()
		return
	}

	ctx := c.Request.Context()

	if clientKey == "" {
		m.logger.Warn(ctx, "Failed to extract client key for rate limiting")
		c.Next()
		return
	}

	// Extract operation
	operation := ""
	if middlewareConfig.CustomOperationExtractor != nil {
		operation = middlewareConfig.CustomOperationExtractor(c)
	} else {
		operation = m.extractOperation(c)
	}

	if operation == "" {
		operation = "default"
	}

	// Check rate limit
	result, err := m.limiter.Allow(ctx, clientKey, operation)
	if err != nil {
		m.logger.ErrorWithErr(ctx, "Rate limit check failed", err)
		c.Next()
		return
	}

	// Add headers based on configuration
	m.addConfiguredHeaders(c, result, operation, middlewareConfig.IncludeHeaders)

	// Log decision
	m.logRateLimitDecision(ctx, clientKey, clientType, operation, result)

	// Handle rate limit exceeded
	if !result.Allowed {
		if middlewareConfig.OnRateLimitExceeded != nil {
			middlewareConfig.OnRateLimitExceeded(c, result)
		} else {
			m.handleRateLimitExceeded(c, result)
		}
		return
	}

	c.Next()
}

// addConfiguredHeaders adds headers based on configuration
func (m *ginMiddleware) addConfiguredHeaders(c *gin.Context, result *Result, operation string, headers RateLimitHeaders) {
	if headers.Limit {
		c.Header("X-RateLimit-Limit", strconv.FormatInt(result.Limit, 10))
	}

	if headers.Remaining {
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(result.Remaining, 10))
	}

	if headers.Reset && !result.ResetTime.IsZero() {
		c.Header("X-RateLimit-Reset", strconv.FormatInt(result.ResetTime.Unix(), 10))
	}

	if headers.RetryAfter && !result.Allowed && result.RetryAfter > 0 {
		c.Header("Retry-After", strconv.FormatInt(int64(result.RetryAfter.Seconds()), 10))
	}

	if headers.Operation {
		c.Header("X-RateLimit-Operation", operation)
	}

	if headers.Used && result.TotalRequests > 0 {
		c.Header("X-RateLimit-Used", strconv.FormatInt(result.TotalRequests, 10))
	}
}
