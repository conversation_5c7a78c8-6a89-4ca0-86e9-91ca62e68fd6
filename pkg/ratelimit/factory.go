package ratelimit

import (
	"fmt"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// NewFromConfig creates a new rate limiter from the application configuration
func NewFromConfig(appConfig *config.Config, logger logger.Logger) (*Limiter, error) {
	if appConfig == nil {
		return nil, fmt.Errorf("application config cannot be nil")
	}

	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// Resolve rate limiting configuration
	rateLimitConfig, err := ResolveConfig(appConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve rate limit config: %w", err)
	}

	// Create rate limiter
	limiter, err := New(rateLimitConfig, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create rate limiter: %w", err)
	}

	return limiter, nil
}

// NewDisabled creates a disabled rate limiter that allows all requests
func NewDisabled(logger logger.Logger) *Limiter {
	return &Limiter{
		config: &Config{Enabled: false},
		logger: logger,
	}
}
