package strategies

import (
	"context"
	"fmt"
	"time"

	"resumatter/pkg/ratelimit/backends"
)

// FixedWindowStrategy implements the fixed window rate limiting algorithm
type FixedWindowStrategy struct {
	windowDuration time.Duration // Duration of each fixed window
}

// NewFixedWindowStrategy creates a new fixed window strategy
func NewFixedWindowStrategy(config StrategyConfig) (*FixedWindowStrategy, error) {
	windowDuration := config.WindowDuration
	if windowDuration == 0 {
		windowDuration = time.Minute // Default: 1 minute windows
	}

	return &FixedWindowStrategy{
		windowDuration: windowDuration,
	}, nil
}

// Allow checks if a request should be allowed using the fixed window algorithm
func (fw *FixedWindowStrategy) Allow(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*StrategyResult, error) {
	now := time.Now()

	// Calculate current window start time
	windowStart := fw.calculateWindowStart(now)

	// Get current window data
	data, err := backend.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get window state: %w", err)
	}

	// Initialize or check if we need a new window
	if data == nil || !windowStart.Equal(data.WindowStart) {
		// New window - reset count
		data = &backends.BackendData{
			Count:       0,
			WindowStart: windowStart,
			LastRequest: now,
			Tokens:      0,
			LastRefill:  now,
			Metadata:    make(map[string]any),
		}
	}

	// Check if request is allowed
	allowed := data.Count < limit

	if allowed {
		// Increment count
		data.Count++
	}

	// Update last request time
	data.LastRequest = now

	// Calculate window end time
	windowEnd := windowStart.Add(fw.windowDuration)

	// Calculate remaining requests
	remaining := max(limit-data.Count, 0)

	// Calculate retry after (time until next window)
	var retryAfter time.Duration
	if !allowed {
		retryAfter = max(windowEnd.Sub(now), 0)
	}

	// Store updated state with TTL
	ttl := fw.windowDuration * 2 // TTL is 2x window duration for safety
	if err := backend.Set(ctx, key, data, ttl); err != nil {
		return nil, fmt.Errorf("failed to update window state: %w", err)
	}

	return &StrategyResult{
		Allowed:       allowed,
		Remaining:     remaining,
		ResetTime:     windowEnd,
		RetryAfter:    retryAfter,
		TotalRequests: data.Count,
		WindowStart:   windowStart,
	}, nil
}

// GetStatus returns the current status of the fixed window
func (fw *FixedWindowStrategy) GetStatus(ctx context.Context, backend backends.Backend, key string, limit int64, window time.Duration) (*StrategyStatus, error) {
	now := time.Now()

	// Calculate current window start time
	windowStart := fw.calculateWindowStart(now)

	// Get current window data
	data, err := backend.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get window state: %w", err)
	}

	// If no data or different window, return fresh window status
	if data == nil || !windowStart.Equal(data.WindowStart) {
		return &StrategyStatus{
			Limit:          limit,
			Remaining:      limit,
			ResetTime:      windowStart.Add(fw.windowDuration),
			WindowStart:    windowStart,
			TotalRequests:  0,
			WindowDuration: fw.windowDuration,
		}, nil
	}

	// Calculate remaining requests
	remaining := max(limit-data.Count, 0)

	return &StrategyStatus{
		Limit:          limit,
		Remaining:      remaining,
		ResetTime:      windowStart.Add(fw.windowDuration),
		WindowStart:    windowStart,
		TotalRequests:  data.Count,
		WindowDuration: fw.windowDuration,
	}, nil
}

// Reset clears the fixed window state for the given key
func (fw *FixedWindowStrategy) Reset(ctx context.Context, backend backends.Backend, key string) error {
	return backend.Delete(ctx, key)
}

// Name returns the name of the strategy
func (fw *FixedWindowStrategy) Name() string {
	return "fixed_window"
}

// calculateWindowStart calculates the start time of the current window
func (fw *FixedWindowStrategy) calculateWindowStart(now time.Time) time.Time {
	// Truncate to window boundary
	// For example, if window is 1 minute, truncate to the minute boundary
	return now.Truncate(fw.windowDuration)
}

// GetWindowInfo returns detailed information about the fixed window configuration
func (fw *FixedWindowStrategy) GetWindowInfo() FixedWindowInfo {
	return FixedWindowInfo{
		WindowDuration: fw.windowDuration,
	}
}

// FixedWindowInfo holds information about fixed window configuration
type FixedWindowInfo struct {
	WindowDuration time.Duration
}

// SetWindowDuration updates the window duration (use with caution in production)
func (fw *FixedWindowStrategy) SetWindowDuration(duration time.Duration) {
	if duration > 0 {
		fw.windowDuration = duration
	}
}

// CalculateOptimalWindowDuration calculates optimal window duration for given requirements
func CalculateOptimalWindowDuration(requestsPerMinute int64, desiredGranularity time.Duration) time.Duration {
	if requestsPerMinute <= 0 {
		return time.Minute
	}

	// For low rates, use longer windows for efficiency
	if requestsPerMinute <= 10 {
		return time.Minute
	}

	// For medium rates, use shorter windows for better granularity
	if requestsPerMinute <= 60 {
		if desiredGranularity > 0 && desiredGranularity < time.Minute {
			return desiredGranularity
		}
		return 30 * time.Second
	}

	// For high rates, use very short windows
	if desiredGranularity > 0 && desiredGranularity < 30*time.Second {
		return desiredGranularity
	}
	return 15 * time.Second
}

// GetCurrentWindow returns information about the current window
func (fw *FixedWindowStrategy) GetCurrentWindow(now time.Time) WindowInfo {
	windowStart := fw.calculateWindowStart(now)
	windowEnd := windowStart.Add(fw.windowDuration)

	return WindowInfo{
		Start:    windowStart,
		End:      windowEnd,
		Duration: fw.windowDuration,
		Progress: now.Sub(windowStart),
	}
}

// WindowInfo holds information about a specific window
type WindowInfo struct {
	Start    time.Time
	End      time.Time
	Duration time.Duration
	Progress time.Duration
}
