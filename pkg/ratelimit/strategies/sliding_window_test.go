//go:build fast

package strategies

import (
	"context"
	"testing"
	"time"

	"resumatter/pkg/ratelimit/backends"
)

func TestNewSlidingWindowStrategy(t *testing.T) {
	config := StrategyConfig{
		Type:       "sliding_window",
		WindowSize: time.Minute,
		Precision:  10 * time.Second,
	}

	strategy, err := NewSlidingWindowStrategy(config)
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got: %v", err)
	}

	if strategy == nil {
		t.Error("Expected strategy to be created")
	}

	info := strategy.GetWindowInfo()
	if info.WindowSize != time.Minute {
		t.Errorf("Expected window size 1 minute, got: %v", info.WindowSize)
	}

	if info.Precision != 10*time.Second {
		t.<PERSON>rf("Expected precision 10s, got: %v", info.Precision)
	}
}

func TestNewSlidingWindowStrategy_Defaults(t *testing.T) {
	config := StrategyConfig{Type: "sliding_window"}

	strategy, err := NewSlidingWindowStrategy(config)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected no error, got: %v", err)
	}

	info := strategy.GetWindowInfo()
	if info.WindowSize != time.Minute {
		t.Errorf("Expected default window size 1 minute, got: %v", info.WindowSize)
	}

	if info.Precision != 10*time.Second {
		t.Errorf("Expected default precision 10s, got: %v", info.Precision)
	}
}

func TestNewSlidingWindowStrategy_InvalidPrecision(t *testing.T) {
	config := StrategyConfig{
		Type:       "sliding_window",
		WindowSize: time.Minute,
		Precision:  2 * time.Minute, // Precision larger than window size
	}

	_, err := NewSlidingWindowStrategy(config)
	if err == nil {
		t.Error("Expected error for precision larger than window size")
	}

	expectedErr := "precision (2m0s) cannot be larger than window size (1m0s)"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestSlidingWindowStrategy_Name(t *testing.T) {
	strategy, _ := NewSlidingWindowStrategy(StrategyConfig{})

	if strategy.Name() != "sliding_window" {
		t.Errorf("Expected name 'sliding_window', got: %s", strategy.Name())
	}
}

func TestSlidingWindowStrategy_Allow_Stub(t *testing.T) {
	strategy, _ := NewSlidingWindowStrategy(StrategyConfig{})

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-stub"
	limit := int64(10)
	window := time.Minute

	// Should return error indicating stub implementation
	_, err = strategy.Allow(ctx, backend, key, limit, window)
	if err == nil {
		t.Error("Expected error for stub implementation")
	}

	expectedErr := "sliding window strategy is not yet fully implemented - this is a stub"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestSlidingWindowStrategy_GetStatus_Stub(t *testing.T) {
	strategy, _ := NewSlidingWindowStrategy(StrategyConfig{})

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-stub"
	limit := int64(10)
	window := time.Minute

	// Should return error indicating stub implementation
	_, err = strategy.GetStatus(ctx, backend, key, limit, window)
	if err == nil {
		t.Error("Expected error for stub implementation")
	}

	expectedErr := "sliding window strategy is not yet fully implemented - this is a stub"
	if err.Error() != expectedErr {
		t.Errorf("Expected error '%s', got '%s'", expectedErr, err.Error())
	}
}

func TestSlidingWindowStrategy_Reset(t *testing.T) {
	strategy, _ := NewSlidingWindowStrategy(StrategyConfig{})

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-reset"

	// Reset should work (just deletes the key)
	err = strategy.Reset(ctx, backend, key)
	if err != nil {
		t.Errorf("Expected no error for reset, got: %v", err)
	}
}

func TestSlidingWindowStrategy_SetWindowSize(t *testing.T) {
	strategy, _ := NewSlidingWindowStrategy(StrategyConfig{
		WindowSize: time.Minute,
		Precision:  10 * time.Second,
	})

	newSize := 2 * time.Minute
	strategy.SetWindowSize(newSize)

	info := strategy.GetWindowInfo()
	if info.WindowSize != newSize {
		t.Errorf("Expected window size %v, got: %v", newSize, info.WindowSize)
	}

	// Test invalid size (smaller than precision, should be ignored)
	strategy.SetWindowSize(5 * time.Second)
	info = strategy.GetWindowInfo()
	if info.WindowSize != newSize {
		t.Error("Invalid window size should be ignored")
	}
}

func TestSlidingWindowStrategy_SetPrecision(t *testing.T) {
	strategy, _ := NewSlidingWindowStrategy(StrategyConfig{
		WindowSize: time.Minute,
		Precision:  10 * time.Second,
	})

	newPrecision := 5 * time.Second
	strategy.SetPrecision(newPrecision)

	info := strategy.GetWindowInfo()
	if info.Precision != newPrecision {
		t.Errorf("Expected precision %v, got: %v", newPrecision, info.Precision)
	}

	// Test invalid precision (larger than window size, should be ignored)
	strategy.SetPrecision(2 * time.Minute)
	info = strategy.GetWindowInfo()
	if info.Precision != newPrecision {
		t.Error("Invalid precision should be ignored")
	}
}

func TestCalculateOptimalPrecision(t *testing.T) {
	tests := []struct {
		name              string
		windowSize        time.Duration
		requestsPerMinute int64
		expectedPrecision time.Duration
	}{
		{
			name:              "high rate",
			windowSize:        time.Minute,
			requestsPerMinute: 200,
			expectedPrecision: 3 * time.Second, // 60s / 20 = 3s
		},
		{
			name:              "medium rate",
			windowSize:        time.Minute,
			requestsPerMinute: 50,
			expectedPrecision: 6 * time.Second, // 60s / 10 = 6s
		},
		{
			name:              "low rate",
			windowSize:        time.Minute,
			requestsPerMinute: 10,
			expectedPrecision: 10 * time.Second, // 60s / 6 = 10s
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			precision := CalculateOptimalPrecision(tt.windowSize, tt.requestsPerMinute)

			if precision != tt.expectedPrecision {
				t.Errorf("Expected precision %v, got: %v", tt.expectedPrecision, precision)
			}
		})
	}
}
