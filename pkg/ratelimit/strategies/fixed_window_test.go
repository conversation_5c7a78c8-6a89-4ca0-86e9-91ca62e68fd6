//go:build fast

package strategies

import (
	"context"
	"testing"
	"time"

	"resumatter/pkg/ratelimit/backends"
)

func TestNewFixedWindowStrategy(t *testing.T) {
	config := StrategyConfig{
		Type:           "fixed_window",
		WindowDuration: 30 * time.Second,
	}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got: %v", err)
	}

	if strategy == nil {
		t.Error("Expected strategy to be created")
	}

	info := strategy.GetWindowInfo()
	if info.WindowDuration != 30*time.Second {
		t.Errorf("Expected window duration 30s, got: %v", info.WindowDuration)
	}
}

func TestNewFixedWindowStrategy_Defaults(t *testing.T) {
	config := StrategyConfig{Type: "fixed_window"}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.<PERSON>("Expected no error, got: %v", err)
	}

	info := strategy.GetWindowInfo()
	if info.WindowDuration != time.Minute {
		t.<PERSON><PERSON>rf("Expected default window duration 1 minute, got: %v", info.WindowDuration)
	}
}

func TestFixedWindowStrategy_Name(t *testing.T) {
	strategy, _ := NewFixedWindowStrategy(StrategyConfig{})

	if strategy.Name() != "fixed_window" {
		t.Errorf("Expected name 'fixed_window', got: %s", strategy.Name())
	}
}

func TestFixedWindowStrategy_Allow_NewWindow(t *testing.T) {
	config := StrategyConfig{
		Type:           "fixed_window",
		WindowDuration: time.Minute,
	}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	// Create memory backend for testing
	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-new-window"
	limit := int64(10)
	window := time.Minute

	// First request should be allowed (new window)
	result, err := strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if !result.Allowed {
		t.Error("Expected first request to be allowed")
	}

	if result.Remaining != 9 { // Started with 10, consumed 1
		t.Errorf("Expected remaining 9, got: %d", result.Remaining)
	}

	if result.TotalRequests != 1 {
		t.Errorf("Expected total requests 1, got: %d", result.TotalRequests)
	}
}

func TestFixedWindowStrategy_Allow_ConsumeRequests(t *testing.T) {
	config := StrategyConfig{
		Type:           "fixed_window",
		WindowDuration: time.Minute,
	}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-consume"
	limit := int64(3) // Small limit for testing
	window := time.Minute

	// Consume all requests in window
	for i := 0; i < 3; i++ {
		result, err := strategy.Allow(ctx, backend, key, limit, window)
		if err != nil {
			t.Errorf("Request %d failed: %v", i+1, err)
		}

		if !result.Allowed {
			t.Errorf("Request %d should be allowed", i+1)
		}

		expectedRemaining := int64(2 - i)
		if result.Remaining != expectedRemaining {
			t.Errorf("Request %d: expected remaining %d, got: %d", i+1, expectedRemaining, result.Remaining)
		}
	}

	// Next request should be denied (window exhausted)
	result, err := strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if result.Allowed {
		t.Error("Request should be denied when window is exhausted")
	}

	if result.Remaining != 0 {
		t.Errorf("Expected remaining 0, got: %d", result.Remaining)
	}

	if result.RetryAfter == 0 {
		t.Error("Expected retry after to be set when denied")
	}
}

func TestFixedWindowStrategy_Allow_WindowReset(t *testing.T) {
	config := StrategyConfig{
		Type:           "fixed_window",
		WindowDuration: 100 * time.Millisecond, // Very short window for testing
	}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-window-reset"
	limit := int64(2)
	window := time.Minute

	// Consume all requests in current window
	for i := 0; i < 2; i++ {
		result, err := strategy.Allow(ctx, backend, key, limit, window)
		if err != nil {
			t.Errorf("Request %d failed: %v", i+1, err)
		}
		if !result.Allowed {
			t.Errorf("Request %d should be allowed", i+1)
		}
	}

	// Next request should be denied
	result, err := strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result.Allowed {
		t.Error("Request should be denied when window is exhausted")
	}

	// Wait for window to reset
	time.Sleep(150 * time.Millisecond)

	// Should be allowed again (new window)
	result, err = strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error after window reset, got: %v", err)
	}
	if !result.Allowed {
		t.Error("Request should be allowed after window reset")
	}

	// Should have full quota again
	if result.Remaining != 1 { // Started with 2, consumed 1
		t.Errorf("Expected remaining 1 after window reset, got: %d", result.Remaining)
	}
}

func TestFixedWindowStrategy_GetStatus(t *testing.T) {
	config := StrategyConfig{
		Type:           "fixed_window",
		WindowDuration: time.Minute,
	}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-status"
	limit := int64(10)
	window := time.Minute

	// Get status for non-existent window
	status, err := strategy.GetStatus(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if status.Limit != limit {
		t.Errorf("Expected limit %d, got: %d", limit, status.Limit)
	}

	if status.Remaining != limit {
		t.Errorf("Expected remaining %d for new window, got: %d", limit, status.Remaining)
	}

	if status.WindowDuration != time.Minute {
		t.Errorf("Expected window duration 1 minute, got: %v", status.WindowDuration)
	}

	// Consume some requests
	strategy.Allow(ctx, backend, key, limit, window)
	strategy.Allow(ctx, backend, key, limit, window)

	// Get status again
	status, err = strategy.GetStatus(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if status.Remaining != 8 { // Started with 10, consumed 2
		t.Errorf("Expected remaining 8, got: %d", status.Remaining)
	}

	if status.TotalRequests != 2 {
		t.Errorf("Expected total requests 2, got: %d", status.TotalRequests)
	}
}

func TestFixedWindowStrategy_Reset(t *testing.T) {
	strategy, _ := NewFixedWindowStrategy(StrategyConfig{})

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-reset"
	limit := int64(10)
	window := time.Minute

	// Use some requests
	strategy.Allow(ctx, backend, key, limit, window)
	strategy.Allow(ctx, backend, key, limit, window)

	// Verify requests were consumed
	status, _ := strategy.GetStatus(ctx, backend, key, limit, window)
	if status.Remaining == limit {
		t.Error("Expected requests to be consumed before reset")
	}

	// Reset the window
	err = strategy.Reset(ctx, backend, key)
	if err != nil {
		t.Errorf("Expected no error for reset, got: %v", err)
	}

	// Verify window is reset (should be full again)
	status, _ = strategy.GetStatus(ctx, backend, key, limit, window)
	if status.Remaining != limit {
		t.Errorf("Expected full window after reset, got remaining: %d", status.Remaining)
	}
}

func TestCalculateOptimalWindowDuration(t *testing.T) {
	tests := []struct {
		name               string
		requestsPerMinute  int64
		desiredGranularity time.Duration
		expectedDuration   time.Duration
	}{
		{
			name:              "low rate",
			requestsPerMinute: 5,
			expectedDuration:  time.Minute,
		},
		{
			name:              "medium rate",
			requestsPerMinute: 30,
			expectedDuration:  30 * time.Second,
		},
		{
			name:               "medium rate with custom granularity",
			requestsPerMinute:  30,
			desiredGranularity: 10 * time.Second,
			expectedDuration:   10 * time.Second,
		},
		{
			name:              "high rate",
			requestsPerMinute: 120,
			expectedDuration:  15 * time.Second,
		},
		{
			name:               "high rate with custom granularity",
			requestsPerMinute:  120,
			desiredGranularity: 5 * time.Second,
			expectedDuration:   5 * time.Second,
		},
		{
			name:              "zero rate",
			requestsPerMinute: 0,
			expectedDuration:  time.Minute,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			duration := CalculateOptimalWindowDuration(tt.requestsPerMinute, tt.desiredGranularity)

			if duration != tt.expectedDuration {
				t.Errorf("Expected duration %v, got: %v", tt.expectedDuration, duration)
			}
		})
	}
}

func TestFixedWindowStrategy_SetWindowDuration(t *testing.T) {
	strategy, _ := NewFixedWindowStrategy(StrategyConfig{})

	newDuration := 30 * time.Second
	strategy.SetWindowDuration(newDuration)

	info := strategy.GetWindowInfo()
	if info.WindowDuration != newDuration {
		t.Errorf("Expected window duration %v, got: %v", newDuration, info.WindowDuration)
	}

	// Test invalid duration (should be ignored)
	strategy.SetWindowDuration(0)
	info = strategy.GetWindowInfo()
	if info.WindowDuration != newDuration {
		t.Error("Invalid window duration should be ignored")
	}
}

func TestFixedWindowStrategy_GetCurrentWindow(t *testing.T) {
	config := StrategyConfig{
		Type:           "fixed_window",
		WindowDuration: time.Minute,
	}

	strategy, err := NewFixedWindowStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	now := time.Now()
	windowInfo := strategy.GetCurrentWindow(now)

	if windowInfo.Duration != time.Minute {
		t.Errorf("Expected duration 1 minute, got: %v", windowInfo.Duration)
	}

	if windowInfo.End.Sub(windowInfo.Start) != time.Minute {
		t.Error("Expected window end to be 1 minute after start")
	}

	if windowInfo.Progress < 0 || windowInfo.Progress > time.Minute {
		t.Errorf("Expected progress to be between 0 and 1 minute, got: %v", windowInfo.Progress)
	}

	// Verify window start is truncated to minute boundary
	expectedStart := now.Truncate(time.Minute)
	if !windowInfo.Start.Equal(expectedStart) {
		t.Errorf("Expected window start %v, got: %v", expectedStart, windowInfo.Start)
	}
}
