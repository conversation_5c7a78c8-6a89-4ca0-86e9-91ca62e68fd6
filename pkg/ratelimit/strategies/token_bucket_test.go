//go:build fast

package strategies

import (
	"context"
	"testing"
	"time"

	"resumatter/pkg/ratelimit/backends"
)

func TestNewTokenBucketStrategy(t *testing.T) {
	config := StrategyConfig{
		Type:         "token_bucket",
		RefillRate:   time.Minute,
		RefillAmount: 10,
	}

	strategy, err := NewTokenBucketStrategy(config)
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got: %v", err)
	}

	if strategy == nil {
		t.Error("Expected strategy to be created")
	}

	info := strategy.GetBucketInfo()
	if info.RefillRate != time.Minute {
		t.Errorf("Expected refill rate 1 minute, got: %v", info.RefillRate)
	}

	if info.RefillAmount != 10 {
		t.<PERSON><PERSON><PERSON>("Expected refill amount 10, got: %d", info.RefillAmount)
	}
}

func TestNewTokenBucketStrategy_Defaults(t *testing.T) {
	config := StrategyConfig{Type: "token_bucket"}

	strategy, err := NewTokenBucketStrategy(config)
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error, got: %v", err)
	}

	info := strategy.GetBucketInfo()
	if info.RefillRate != time.Minute {
		t.Errorf("Expected default refill rate 1 minute, got: %v", info.RefillRate)
	}

	if info.RefillAmount != 1 {
		t.Errorf("Expected default refill amount 1, got: %d", info.RefillAmount)
	}
}

func TestTokenBucketStrategy_Name(t *testing.T) {
	strategy, _ := NewTokenBucketStrategy(StrategyConfig{})

	if strategy.Name() != "token_bucket" {
		t.Errorf("Expected name 'token_bucket', got: %s", strategy.Name())
	}
}

func TestTokenBucketStrategy_Allow_NewBucket(t *testing.T) {
	config := StrategyConfig{
		Type:         "token_bucket",
		RefillRate:   time.Minute,
		RefillAmount: 1,
	}

	strategy, err := NewTokenBucketStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	// Create memory backend for testing
	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-new-bucket"
	limit := int64(10)
	window := time.Minute

	// First request should be allowed (bucket starts full)
	result, err := strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if !result.Allowed {
		t.Error("Expected first request to be allowed")
	}

	if result.Remaining != 9 { // Started with 10, consumed 1
		t.Errorf("Expected remaining 9, got: %d", result.Remaining)
	}

	if result.TotalRequests != 1 {
		t.Errorf("Expected total requests 1, got: %d", result.TotalRequests)
	}
}

func TestTokenBucketStrategy_Allow_ConsumeTokens(t *testing.T) {
	config := StrategyConfig{
		Type:         "token_bucket",
		RefillRate:   time.Hour, // Very slow refill for testing
		RefillAmount: 1,
	}

	strategy, err := NewTokenBucketStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-consume"
	limit := int64(3) // Small bucket for testing
	window := time.Minute

	// Consume all tokens
	for i := 0; i < 3; i++ {
		result, err := strategy.Allow(ctx, backend, key, limit, window)
		if err != nil {
			t.Errorf("Request %d failed: %v", i+1, err)
		}

		if !result.Allowed {
			t.Errorf("Request %d should be allowed", i+1)
		}

		expectedRemaining := int64(2 - i)
		if result.Remaining != expectedRemaining {
			t.Errorf("Request %d: expected remaining %d, got: %d", i+1, expectedRemaining, result.Remaining)
		}
	}

	// Next request should be denied (bucket empty)
	result, err := strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if result.Allowed {
		t.Error("Request should be denied when bucket is empty")
	}

	if result.Remaining != 0 {
		t.Errorf("Expected remaining 0, got: %d", result.Remaining)
	}

	if result.RetryAfter == 0 {
		t.Error("Expected retry after to be set when denied")
	}
}

func TestTokenBucketStrategy_Allow_Refill(t *testing.T) {
	config := StrategyConfig{
		Type:         "token_bucket",
		RefillRate:   100 * time.Millisecond, // Fast refill for testing
		RefillAmount: 2,
	}

	strategy, err := NewTokenBucketStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-refill"
	limit := int64(5)
	window := time.Minute

	// Consume all tokens
	for i := 0; i < 5; i++ {
		result, err := strategy.Allow(ctx, backend, key, limit, window)
		if err != nil {
			t.Errorf("Request %d failed: %v", i+1, err)
		}
		if !result.Allowed {
			t.Errorf("Request %d should be allowed", i+1)
		}
	}

	// Next request should be denied
	result, err := strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result.Allowed {
		t.Error("Request should be denied when bucket is empty")
	}

	// Wait for refill
	time.Sleep(150 * time.Millisecond)

	// Should have tokens again (2 tokens refilled)
	result, err = strategy.Allow(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error after refill, got: %v", err)
	}
	if !result.Allowed {
		t.Error("Request should be allowed after refill")
	}

	// Should have at least 1 token remaining (2 refilled, 1 consumed, but may have more due to timing)
	if result.Remaining < 1 {
		t.Errorf("Expected remaining >= 1 after refill, got: %d", result.Remaining)
	}
}

func TestTokenBucketStrategy_GetStatus(t *testing.T) {
	config := StrategyConfig{
		Type:         "token_bucket",
		RefillRate:   time.Minute,
		RefillAmount: 1,
	}

	strategy, err := NewTokenBucketStrategy(config)
	if err != nil {
		t.Fatalf("Failed to create strategy: %v", err)
	}

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-status"
	limit := int64(10)
	window := time.Minute

	// Get status for non-existent bucket
	status, err := strategy.GetStatus(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if status.Limit != limit {
		t.Errorf("Expected limit %d, got: %d", limit, status.Limit)
	}

	if status.Remaining != limit {
		t.Errorf("Expected remaining %d for new bucket, got: %d", limit, status.Remaining)
	}

	// Consume some tokens
	strategy.Allow(ctx, backend, key, limit, window)
	strategy.Allow(ctx, backend, key, limit, window)

	// Get status again
	status, err = strategy.GetStatus(ctx, backend, key, limit, window)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if status.Remaining != 8 { // Started with 10, consumed 2
		t.Errorf("Expected remaining 8, got: %d", status.Remaining)
	}

	if status.TotalRequests != 2 {
		t.Errorf("Expected total requests 2, got: %d", status.TotalRequests)
	}
}

func TestTokenBucketStrategy_Reset(t *testing.T) {
	strategy, _ := NewTokenBucketStrategy(StrategyConfig{})

	backend, err := backends.NewMemoryBackend(backends.BackendConfig{Type: "memory"})
	if err != nil {
		t.Fatalf("Failed to create backend: %v", err)
	}
	defer backend.Close()

	ctx := context.Background()
	key := "test-reset"
	limit := int64(10)
	window := time.Minute

	// Use some tokens
	strategy.Allow(ctx, backend, key, limit, window)
	strategy.Allow(ctx, backend, key, limit, window)

	// Verify tokens were consumed
	status, _ := strategy.GetStatus(ctx, backend, key, limit, window)
	if status.Remaining == limit {
		t.Error("Expected tokens to be consumed before reset")
	}

	// Reset the bucket
	err = strategy.Reset(ctx, backend, key)
	if err != nil {
		t.Errorf("Expected no error for reset, got: %v", err)
	}

	// Verify bucket is reset (should be full again)
	status, _ = strategy.GetStatus(ctx, backend, key, limit, window)
	if status.Remaining != limit {
		t.Errorf("Expected full bucket after reset, got remaining: %d", status.Remaining)
	}
}

func TestCalculateOptimalRefillRate(t *testing.T) {
	tests := []struct {
		name              string
		requestsPerMinute int64
		burstSize         int64
		expectRefillRate  time.Duration
		expectAmount      int64
	}{
		{
			name:              "high rate",
			requestsPerMinute: 60,
			burstSize:         10,
			expectRefillRate:  5 * time.Second, // 60/12 = 5 tokens every 5 seconds
			expectAmount:      5,
		},
		{
			name:              "low rate",
			requestsPerMinute: 6,
			burstSize:         3,
			expectRefillRate:  10 * time.Second, // 6/6 = 1 token every 10 seconds
			expectAmount:      1,
		},
		{
			name:              "very low rate",
			requestsPerMinute: 1,
			burstSize:         1,
			expectRefillRate:  time.Minute, // 1 token every minute
			expectAmount:      1,
		},
		{
			name:              "zero rate",
			requestsPerMinute: 0,
			burstSize:         1,
			expectRefillRate:  time.Minute,
			expectAmount:      1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rate, amount := CalculateOptimalRefillRate(tt.requestsPerMinute, tt.burstSize)

			if rate != tt.expectRefillRate {
				t.Errorf("Expected refill rate %v, got: %v", tt.expectRefillRate, rate)
			}

			if amount != tt.expectAmount {
				t.Errorf("Expected refill amount %d, got: %d", tt.expectAmount, amount)
			}
		})
	}
}

func TestTokenBucketStrategy_SetRefillRate(t *testing.T) {
	strategy, _ := NewTokenBucketStrategy(StrategyConfig{})

	newRate := 30 * time.Second
	strategy.SetRefillRate(newRate)

	info := strategy.GetBucketInfo()
	if info.RefillRate != newRate {
		t.Errorf("Expected refill rate %v, got: %v", newRate, info.RefillRate)
	}

	// Test invalid rate (should be ignored)
	strategy.SetRefillRate(0)
	info = strategy.GetBucketInfo()
	if info.RefillRate != newRate {
		t.Error("Invalid refill rate should be ignored")
	}
}

func TestTokenBucketStrategy_SetRefillAmount(t *testing.T) {
	strategy, _ := NewTokenBucketStrategy(StrategyConfig{})

	newAmount := int64(5)
	strategy.SetRefillAmount(newAmount)

	info := strategy.GetBucketInfo()
	if info.RefillAmount != newAmount {
		t.Errorf("Expected refill amount %d, got: %d", newAmount, info.RefillAmount)
	}

	// Test invalid amount (should be ignored)
	strategy.SetRefillAmount(0)
	info = strategy.GetBucketInfo()
	if info.RefillAmount != newAmount {
		t.Error("Invalid refill amount should be ignored")
	}
}
