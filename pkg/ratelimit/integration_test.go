//go:build fast

package ratelimit

import (
	"context"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

func TestRateLimiter_TokenBucket_Integration(t *testing.T) {
	// Create app config with token bucket strategy
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
			},
			Operations: map[string]config.OperationRateLimit{
				"tailor": {
					Enabled:           true,
					RequestsPerMinute: 5,
					RequestsPerHour:   50,
					RequestsPerDay:    500,
					BurstSize:         3,
				},
			},
		},
	}

	// Create rate limiter
	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	ctx := context.Background()
	clientKey := "test-client"
	operation := "tailor"

	// Test burst capability - should allow 5 requests quickly (bucket starts with 5 tokens)
	for i := 0; i < 5; i++ {
		result, err := limiter.Allow(ctx, clientKey, operation)
		if err != nil {
			t.Errorf("Request %d failed: %v", i+1, err)
		}

		if !result.Allowed {
			t.Errorf("Request %d should be allowed (burst)", i+1)
		}

		expectedRemaining := int64(4 - i)
		if result.Remaining != expectedRemaining {
			t.Errorf("Request %d: expected remaining %d, got: %d", i+1, expectedRemaining, result.Remaining)
		}
	}

	// Next request should be denied (bucket empty)
	result, err := limiter.Allow(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if result.Allowed {
		t.Error("Request should be denied when bucket is empty")
	}

	if result.RetryAfter == 0 {
		t.Error("Expected retry after to be set when denied")
	}
}

func TestRateLimiter_TokenBucket_GetStatus(t *testing.T) {
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
			},
		},
	}

	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	ctx := context.Background()
	clientKey := "test-status"
	operation := "analyze" // Uses defaults

	// Get initial status
	status, err := limiter.GetStatus(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if status.Limit != 10 {
		t.Errorf("Expected limit 10, got: %d", status.Limit)
	}

	if status.Remaining != 10 {
		t.Errorf("Expected remaining 10 for new bucket, got: %d", status.Remaining)
	}

	// Consume some tokens
	limiter.Allow(ctx, clientKey, operation)
	limiter.Allow(ctx, clientKey, operation)

	// Get status again
	status, err = limiter.GetStatus(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if status.Remaining != 8 {
		t.Errorf("Expected remaining 8, got: %d", status.Remaining)
	}

	if status.TotalRequests != 2 {
		t.Errorf("Expected total requests 2, got: %d", status.TotalRequests)
	}
}

func TestRateLimiter_TokenBucket_Reset(t *testing.T) {
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 5,
				RequestsPerHour:   50,
				RequestsPerDay:    500,
				BurstSize:         3,
			},
		},
	}

	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	ctx := context.Background()
	clientKey := "test-reset"
	operation := "evaluate"

	// Consume all tokens
	for i := 0; i < 5; i++ {
		limiter.Allow(ctx, clientKey, operation)
	}

	// Verify bucket is empty
	result, err := limiter.Allow(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result.Allowed {
		t.Error("Request should be denied when bucket is empty")
	}

	// Reset the rate limit
	err = limiter.Reset(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error for reset, got: %v", err)
	}

	// Should be able to make requests again
	result, err = limiter.Allow(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error after reset, got: %v", err)
	}
	if !result.Allowed {
		t.Error("Request should be allowed after reset")
	}
}

func TestRateLimiter_FixedWindow_Integration(t *testing.T) {
	// Create app config with fixed window strategy
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "fixed_window",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
			},
			Operations: map[string]config.OperationRateLimit{
				"tailor": {
					Enabled:           true,
					RequestsPerMinute: 5,
					RequestsPerHour:   50,
					RequestsPerDay:    500,
					BurstSize:         3,
				},
			},
		},
	}

	// Create rate limiter
	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	ctx := context.Background()
	clientKey := "test-client-fw"
	operation := "tailor"

	// Test fixed window behavior - should allow 5 requests in window
	for i := 0; i < 5; i++ {
		result, err := limiter.Allow(ctx, clientKey, operation)
		if err != nil {
			t.Errorf("Request %d failed: %v", i+1, err)
		}

		if !result.Allowed {
			t.Errorf("Request %d should be allowed (within window)", i+1)
		}

		expectedRemaining := int64(4 - i)
		if result.Remaining != expectedRemaining {
			t.Errorf("Request %d: expected remaining %d, got: %d", i+1, expectedRemaining, result.Remaining)
		}
	}

	// Next request should be denied (window exhausted)
	result, err := limiter.Allow(ctx, clientKey, operation)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if result.Allowed {
		t.Error("Request should be denied when window is exhausted")
	}

	if result.RetryAfter == 0 {
		t.Error("Expected retry after to be set when denied")
	}
}

func TestRateLimiter_TokenBucket_DifferentOperations(t *testing.T) {
	appConfig := &config.Config{
		RateLimit: config.RateLimitConfig{
			Enabled:  true,
			Backend:  "memory",
			Strategy: "token_bucket",
			KeyBy:    "ip",
			Defaults: config.DefaultRateLimit{
				RequestsPerMinute: 10,
				RequestsPerHour:   100,
				RequestsPerDay:    1000,
				BurstSize:         5,
			},
			Operations: map[string]config.OperationRateLimit{
				"tailor": {
					Enabled:           true,
					RequestsPerMinute: 3,
					RequestsPerHour:   30,
					RequestsPerDay:    300,
					BurstSize:         2,
				},
				"analyze": {
					Enabled:           true,
					RequestsPerMinute: 8,
					RequestsPerHour:   80,
					RequestsPerDay:    800,
					BurstSize:         4,
				},
			},
		},
	}

	log := logger.NewDefault()
	limiter, err := NewFromConfig(appConfig, log)
	if err != nil {
		t.Fatalf("Failed to create rate limiter: %v", err)
	}
	defer limiter.Close()

	ctx := context.Background()
	clientKey := "test-operations"

	// Test tailor operation (limit: 3)
	for i := 0; i < 3; i++ {
		result, err := limiter.Allow(ctx, clientKey, "tailor")
		if err != nil {
			t.Errorf("Tailor request %d failed: %v", i+1, err)
		}
		if !result.Allowed {
			t.Errorf("Tailor request %d should be allowed", i+1)
		}
	}

	// Next tailor request should be denied
	result, err := limiter.Allow(ctx, clientKey, "tailor")
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result.Allowed {
		t.Error("Tailor request should be denied when bucket is empty")
	}

	// Test analyze operation (limit: 8) - should still work
	for i := 0; i < 8; i++ {
		result, err := limiter.Allow(ctx, clientKey, "analyze")
		if err != nil {
			t.Errorf("Analyze request %d failed: %v", i+1, err)
		}
		if !result.Allowed {
			t.Errorf("Analyze request %d should be allowed", i+1)
		}
	}

	// Next analyze request should be denied
	result, err = limiter.Allow(ctx, clientKey, "analyze")
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result.Allowed {
		t.Error("Analyze request should be denied when bucket is empty")
	}
}
