package ratelimit

import (
	"context"
	"time"
)

// RateLimiter defines the main interface for rate limiting operations
type RateLimiter interface {
	// Allow checks if a request should be allowed for the given key and operation
	Allow(ctx context.Context, key string, operation string) (*Result, error)

	// Reset clears the rate limit data for the given key and operation
	Reset(ctx context.Context, key string, operation string) error

	// GetStatus returns the current rate limit status for the given key and operation
	GetStatus(ctx context.Context, key string, operation string) (*Status, error)

	// Close releases any resources held by the rate limiter
	Close() error
}

// Result represents the result of a rate limit check
type Result struct {
	// Allowed indicates whether the request should be allowed
	Allowed bool

	// Remaining is the number of requests remaining in the current window
	Remaining int64

	// ResetTime is when the rate limit window will reset
	ResetTime time.Time

	// RetryAfter is the duration to wait before retrying if not allowed
	RetryAfter time.Duration

	// TotalRequests is the total number of requests made in the current window
	TotalRequests int64

	// Limit is the maximum number of requests allowed in the window
	Limit int64
}

// Status represents the current rate limit status
type Status struct {
	// Limit is the maximum number of requests allowed in the window
	Limit int64

	// Remaining is the number of requests remaining in the current window
	Remaining int64

	// ResetTime is when the rate limit window will reset
	ResetTime time.Time

	// WindowStart is when the current window started
	WindowStart time.Time

	// TotalRequests is the total number of requests made in the current window
	TotalRequests int64

	// WindowDuration is the duration of the rate limit window
	WindowDuration time.Duration
}
