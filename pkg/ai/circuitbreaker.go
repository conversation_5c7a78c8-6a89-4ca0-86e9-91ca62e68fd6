package ai

import (
	"context"
	"fmt"

	"github.com/sony/gobreaker/v2"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// AICircuitBreaker wraps AI operations with circuit breaker pattern
type AICircuitBreaker struct {
	cb               *gobreaker.CircuitBreaker[*providers.GenerationResponse]
	logger           logger.Logger
	metricsCollector MetricsCollector
	operationType    config.OperationType
}

// NewAICircuitBreaker creates a circuit breaker configured for a specific operation type
func NewAICircuitBreaker(operationType config.OperationType, cfg *config.ResolvedOperationConfig, log logger.Logger, metricsCollector MetricsCollector) *AICircuitBreaker {
	// If circuit breaker is disabled, return nil to indicate no circuit breaker
	if !cfg.CircuitBreakerEnabled {
		return nil
	}

	// Create the circuit breaker instance first so we can reference it in the callback
	cb := &AICircuitBreaker{
		logger:           log,
		metricsCollector: metricsCollector,
		operationType:    operationType,
	}

	settings := gobreaker.Settings{
		Name:        fmt.Sprintf("AI-%s", string(operationType)),
		MaxRequests: cfg.CircuitBreakerMaxRequests,
		Interval:    cfg.CircuitBreakerInterval,
		Timeout:     cfg.CircuitBreakerTimeout,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= cfg.CircuitBreakerMinRequests &&
				failureRatio >= cfg.CircuitBreakerFailureThreshold
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			log.Info(context.Background(), "Circuit breaker state changed",
				logger.String("name", name),
				logger.String("operation_type", string(operationType)),
				logger.String("from", from.String()),
				logger.String("to", to.String()),
				logger.String("max_requests", fmt.Sprintf("%d", cfg.CircuitBreakerMaxRequests)),
				logger.String("failure_threshold", fmt.Sprintf("%.2f", cfg.CircuitBreakerFailureThreshold)))

			// Record metrics if collector is available
			if cb.metricsCollector != nil {
				// Convert state to numeric value for metrics
				stateValue := cb.stateToFloat64(to)
				cb.metricsCollector.RecordCircuitBreakerState(context.Background(), string(operationType), stateValue)
			}
		},
	}

	cb.cb = gobreaker.NewCircuitBreaker[*providers.GenerationResponse](settings)
	return cb
}

// Execute executes the provided function with circuit breaker protection
func (cb *AICircuitBreaker) Execute(fn func() (*providers.GenerationResponse, error)) (*providers.GenerationResponse, error) {
	if cb == nil || cb.cb == nil {
		// If breaker is disabled/nil, just execute the function directly
		return fn()
	}
	return cb.cb.Execute(fn)
}

// GetStats returns circuit breaker statistics
func (cb *AICircuitBreaker) GetStats() map[string]any {
	if cb == nil || cb.cb == nil {
		return map[string]any{
			"enabled": false,
		}
	}

	return map[string]any{
		"name":    cb.cb.Name(),
		"state":   cb.cb.State().String(),
		"counts":  cb.cb.Counts(),
		"enabled": true,
	}
}

// IsHealthy returns true if the circuit breaker is in closed state
func (cb *AICircuitBreaker) IsHealthy() bool {
	if cb == nil || cb.cb == nil {
		return true // If no circuit breaker, consider it healthy
	}
	return cb.cb.State() == gobreaker.StateClosed
}

// GetState returns the current circuit breaker state
func (cb *AICircuitBreaker) GetState() string {
	if cb == nil || cb.cb == nil {
		return "disabled"
	}
	return cb.cb.State().String()
}

// GetCounts returns the current circuit breaker counts
func (cb *AICircuitBreaker) GetCounts() gobreaker.Counts {
	if cb == nil || cb.cb == nil {
		return gobreaker.Counts{}
	}
	return cb.cb.Counts()
}

// stateToFloat64 converts circuit breaker state to numeric value for metrics
func (cb *AICircuitBreaker) stateToFloat64(state gobreaker.State) float64 {
	switch state {
	case gobreaker.StateClosed:
		return 0.0 // Healthy state
	case gobreaker.StateOpen:
		return 1.0 // Unhealthy state
	case gobreaker.StateHalfOpen:
		return 2.0 // Recovery state
	default:
		return -1.0 // Unknown state
	}
}

// SetMetricsCollector sets the metrics collector for the circuit breaker
func (cb *AICircuitBreaker) SetMetricsCollector(collector MetricsCollector) {
	if cb != nil {
		cb.metricsCollector = collector
	}
}
