package ai

import "resumatter/pkg/ai/providers"

// buildTailorSchema builds the JSON schema for tailor resume response
func (c *Client) buildTailorSchema() *providers.ResponseSchema {
	return &providers.ResponseSchema{
		Type: "object",
		Properties: map[string]any{
			"tailoredResume": map[string]any{"type": "string"},
			"atsAnalysis": map[string]any{
				"type": "object",
				"properties": map[string]any{
					"score":      map[string]any{"type": "integer"},
					"strengths":  map[string]any{"type": "string"},
					"weaknesses": map[string]any{"type": "string"},
				},
				"required": []string{"score", "strengths", "weaknesses"},
			},
			"jobPostingAnalysis": map[string]any{
				"type": "object",
				"properties": map[string]any{
					"clarity":     map[string]any{"type": "string"},
					"inclusivity": map[string]any{"type": "string"},
					"quality":     map[string]any{"type": "string"},
				},
				"required": []string{"clarity", "inclusivity", "quality"},
			},
		},
		Required: []string{"tailoredResume", "atsAnalysis", "jobPostingAnalysis"},
	}
}

// buildEvaluateSchema builds the JSON schema for evaluate resume response
func (c *Client) buildEvaluateSchema() *providers.ResponseSchema {
	return &providers.ResponseSchema{
		Type: "object",
		Properties: map[string]any{
			"summary": map[string]any{"type": "string"},
			"findings": map[string]any{
				"type": "array",
				"items": map[string]any{
					"type": "object",
					"properties": map[string]any{
						"type":        map[string]any{"type": "string"},
						"description": map[string]any{"type": "string"},
						"evidence":    map[string]any{"type": "string"},
					},
					"required": []string{"type", "description", "evidence"},
				},
			},
		},
		Required: []string{"summary", "findings"},
	}
}

// buildAnalyzeSchema builds the JSON schema for analyze job response
func (c *Client) buildAnalyzeSchema() *providers.ResponseSchema {
	return &providers.ResponseSchema{
		Type: "object",
		Properties: map[string]any{
			"jobQualityScore": map[string]any{"type": "integer"},
			"clarity": map[string]any{
				"type": "object",
				"properties": map[string]any{
					"score":    map[string]any{"type": "integer"},
					"analysis": map[string]any{"type": "string"},
					"improvements": map[string]any{
						"type":  "array",
						"items": map[string]any{"type": "string"},
					},
				},
				"required": []string{"score", "analysis", "improvements"},
			},
			"inclusivity": map[string]any{
				"type": "object",
				"properties": map[string]any{
					"score":    map[string]any{"type": "integer"},
					"analysis": map[string]any{"type": "string"},
					"flaggedTerms": map[string]any{
						"type":  "array",
						"items": map[string]any{"type": "string"},
					},
					"suggestions": map[string]any{
						"type":  "array",
						"items": map[string]any{"type": "string"},
					},
				},
				"required": []string{"score", "analysis", "flaggedTerms", "suggestions"},
			},
			"candidateAttraction": map[string]any{
				"type": "object",
				"properties": map[string]any{
					"score": map[string]any{"type": "integer"},
					"strengths": map[string]any{
						"type":  "array",
						"items": map[string]any{"type": "string"},
					},
					"weaknesses": map[string]any{
						"type":  "array",
						"items": map[string]any{"type": "string"},
					},
				},
				"required": []string{"score", "strengths", "weaknesses"},
			},
			"marketCompetitiveness": map[string]any{
				"type": "object",
				"properties": map[string]any{
					"salaryTransparency":  map[string]any{"type": "string"},
					"requirementsRealism": map[string]any{"type": "string"},
					"industryAlignment":   map[string]any{"type": "string"},
				},
				"required": []string{"salaryTransparency", "requirementsRealism", "industryAlignment"},
			},
			"recommendations": map[string]any{
				"type":  "array",
				"items": map[string]any{"type": "string"},
			},
		},
		Required: []string{"jobQualityScore", "clarity", "inclusivity", "candidateAttraction", "marketCompetitiveness", "recommendations"},
	}
}
