package providers

import (
	"context"
	"fmt"

	"resumatter/pkg"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// AnthropicProvider implements Provider interface for Anthropic
type AnthropicProvider struct {
	config *config.AnthropicConfig
	logger logger.Logger
}

// NewAnthropicProvider creates a new Anthropic provider
func NewAnthropicProvider(cfg *config.AnthropicConfig, logger logger.Logger) (*AnthropicProvider, error) {
	return &AnthropicProvider{
		config: cfg,
		logger: logger.Named(pkg.PROVIDER_ANTHROPIC),
	}, nil
}

// GenerateStructuredContent generates structured content using Anthropic
func (p *AnthropicProvider) GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error) {
	return nil, fmt.Errorf("%s provider not yet implemented", pkg.PROVIDER_ANTHROPIC)
}

// Name returns the provider name
func (p *AnthropicProvider) Name() string {
	return pkg.PROVIDER_ANTHROPIC
}

// SupportedModels returns the list of supported models
func (p *AnthropicProvider) SupportedModels() []string {
	return []string{}
}

// GetModelInfo retrieves information about a specific model
func (p *AnthropicProvider) GetModelInfo(ctx context.Context, modelName string) (*ModelInfo, error) {
	return nil, fmt.Errorf("%s provider GetModelInfo not yet implemented", pkg.PROVIDER_ANTHROPIC)
}

// Close closes the provider
func (p *AnthropicProvider) Close() error {
	return nil
}
