package providers

import (
	"context"
	"fmt"

	"resumatter/pkg"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// OpenAIProvider implements Provider interface for OpenAI
type OpenAIProvider struct {
	config *config.OpenAIConfig
	logger logger.Logger
}

// NewOpenAIProvider creates a new OpenAI provider
func NewOpenAIProvider(cfg *config.OpenAIConfig, logger logger.Logger) (*OpenAIProvider, error) {
	return &OpenAIProvider{
		config: cfg,
		logger: logger.Named(pkg.PROVIDER_OPENAI),
	}, nil
}

// GenerateStructuredContent generates structured content using OpenAI
func (p *OpenAIProvider) GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error) {
	return nil, fmt.Errorf("%s provider not yet implemented", pkg.PROVIDER_OPENAI)
}

// Name returns the provider name
func (p *OpenAIProvider) Name() string {
	return pkg.PROVIDER_OPENAI
}

// SupportedModels returns the list of supported models
func (p *OpenAIProvider) SupportedModels() []string {
	return []string{}
}

// GetModelInfo retrieves information about a specific model
func (p *OpenAIProvider) GetModelInfo(ctx context.Context, modelName string) (*ModelInfo, error) {
	return nil, fmt.Errorf("%s provider GetModelInfo not yet implemented", pkg.PROVIDER_OPENAI)
}

// Close closes the provider
func (p *OpenAIProvider) Close() error {
	return nil
}
