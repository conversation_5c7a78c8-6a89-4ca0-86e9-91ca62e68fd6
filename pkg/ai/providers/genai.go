package providers

import (
	"context"
	"fmt"

	"google.golang.org/genai"

	"resumatter/pkg"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// GenAIBackendType represents the type of GenAI backend

// GenAIProvider implements Provider interface for both Gemini API and Vertex AI
type GenA<PERSON>rovider struct {
	client *genai.Client
	config *config.GenAIConfig
	logger logger.Logger
}

// NewGenAIProvider creates a new unified GenAI provider
func NewGenAIProvider(cfg *config.GenAIConfig, logger logger.Logger) (*GenAIProvider, error) {
	var clientConfig *genai.ClientConfig

	// Determine client configuration based on backend
	switch cfg.Backend {
	case pkg.PROVIDER_GENAI_GEMINI:
		clientConfig = &genai.ClientConfig{
			Backend: genai.BackendGeminiAPI,
		}
	case pkg.PROVIDER_GENAI_VERTEXAI:
		clientConfig = &genai.ClientConfig{
			Backend:  genai.BackendVertexAI,
			Project:  cfg.GoogleCloudProject,
			Location: cfg.GoogleCloudRegion,
		}
	default:
		return nil, fmt.Errorf("unsupported GenAI backend: %s", cfg.Backend)
	}

	client, err := genai.NewClient(context.Background(), clientConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create GenAI client for backend %s: %w", cfg.Backend, err)
	}

	return &GenAIProvider{
		client: client,
		config: cfg,
		logger: logger.Named(cfg.Backend),
	}, nil
}

// GenerateStructuredContent generates structured content using GenAI
func (p *GenAIProvider) GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error) {
	genaiConfig := &genai.GenerateContentConfig{
		ResponseMIMEType:  "application/json",
		Temperature:       &request.Config.Temperature,
		SystemInstruction: genai.NewContentFromText(request.SystemPrompt, genai.RoleUser),
	}

	// Set max tokens if specified
	if request.Config.MaxTokens > 0 {
		maxTokens := int32(request.Config.MaxTokens)
		genaiConfig.MaxOutputTokens = maxTokens
	}

	// Set response schema if provided
	if request.Schema != nil {
		genaiConfig.ResponseSchema = genaiConvertSchema(request.Schema)
	}

	// Create context with timeout
	timeoutCtx := ctx
	if request.Config.Timeout > 0 {
		var cancel context.CancelFunc
		timeoutCtx, cancel = context.WithTimeout(ctx, request.Config.Timeout)
		defer cancel()
	}

	result, err := p.client.Models.GenerateContent(
		timeoutCtx,
		request.Config.Model,
		genai.Text(request.UserPrompt),
		genaiConfig,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response
	if len(result.Candidates) == 0 || result.Candidates[0].Content == nil || len(result.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("received empty response from %s", p.config.Backend)
	}

	// Build metadata based on backend type
	metadata := map[string]any{
		"model":      request.Config.Model,
		"provider":   p.Name(),
		"candidates": len(result.Candidates),
	}

	// Add backend-specific metadata
	if p.config.Backend == pkg.PROVIDER_GENAI_VERTEXAI {
		metadata["project"] = p.config.GoogleCloudProject
		metadata["region"] = p.config.GoogleCloudRegion
	}

	response := &GenerationResponse{
		Content:  result.Text(),
		Metadata: metadata,
	}

	// Extract usage stats if available
	if result.UsageMetadata != nil {
		response.Usage = &UsageStats{
			PromptTokens:     int(result.UsageMetadata.PromptTokenCount),
			CompletionTokens: int(result.UsageMetadata.CandidatesTokenCount),
			TotalTokens:      int(result.UsageMetadata.TotalTokenCount),
		}
	}

	return response, nil
}

// Name returns the provider name
func (p *GenAIProvider) Name() string {
	return p.config.Backend
}

// SupportedModels returns the list of supported models based on backend
func (p *GenAIProvider) SupportedModels() []string {
	switch p.config.Backend {
	case pkg.PROVIDER_GENAI_GEMINI:
		return []string{
			"gemini-2.0-flash",
			"gemini-2.0-flash-lite",
			"gemini-1.5-pro",
			"gemini-1.5-flash",
		}
	case pkg.PROVIDER_GENAI_VERTEXAI:
		return []string{
			"gemini-2.5-pro",
			"gemini-2.5-flash",
			"gemini-2.0-flash",
			"gemini-2.0-flash-lite",
			"gemini-1.5-pro",
			"gemini-1.5-flash",
		}
	default:
		return []string{}
	}
}

// GetModelInfo retrieves information about a specific model
func (p *GenAIProvider) GetModelInfo(ctx context.Context, modelName string) (*ModelInfo, error) {
	model, err := p.client.Models.Get(ctx, modelName, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get model info for %s: %w", modelName, err)
	}

	return &ModelInfo{
		Name:             model.Name,
		DisplayName:      model.DisplayName,
		Description:      model.Description,
		Version:          model.Version,
		InputTokenLimit:  model.InputTokenLimit,
		OutputTokenLimit: model.OutputTokenLimit,
		SupportedActions: model.SupportedActions,
		Labels:           model.Labels,
		Provider:         p.Name(),
	}, nil
}

// Close closes the provider
func (p *GenAIProvider) Close() error {
	// genai.Client doesn't have a Close method in the current version
	return nil
}
