package providers

import (
	"google.golang.org/genai"
)

// genaiConvertSchema converts our schema format to genai schema format
func genaiConvertSchema(schema *ResponseSchema) *genai.Schema {
	return &genai.Schema{
		Type:       genai.TypeObject,
		Properties: genaiConvertProperties(schema.Properties),
		Required:   schema.Required,
	}
}

// genaiConvertProperties converts property definitions
func genaiConvertProperties(props map[string]any) map[string]*genai.Schema {
	result := make(map[string]*genai.Schema)
	for key, value := range props {
		if propMap, ok := value.(map[string]any); ok {
			result[key] = genaiBuildPropertySchema(propMap)
		}
	}
	return result
}

// genaiBuildPropertySchema builds a genai.Schema from a property map
func genaiBuildPropertySchema(propMap map[string]any) *genai.Schema {
	propSchema := &genai.Schema{}
	if typeVal, exists := propMap["type"]; exists {
		if typeStr, ok := typeVal.(string); ok {
			genaiFillPropertySchema(propSchema, typeStr, propMap)
		}
	}
	return propSchema
}

// genaiFillPropertySchema fills a genai.Schema based on type and property map
func genaiFillPropertySchema(propSchema *genai.Schema, typeStr string, propMap map[string]any) {
	switch typeStr {
	case "string":
		propSchema.Type = genai.TypeString
	case "number":
		propSchema.Type = genai.TypeNumber
	case "integer":
		propSchema.Type = genai.TypeInteger
	case "boolean":
		propSchema.Type = genai.TypeBoolean
	case "array":
		propSchema.Type = genai.TypeArray
		// Handle array items
		if items, exists := propMap["items"]; exists {
			if itemsMap, ok := items.(map[string]any); ok {
				propSchema.Items = genaiConvertSingleProperty(itemsMap)
			}
		}
	case "object":
		propSchema.Type = genai.TypeObject
		// Handle nested properties
		if properties, exists := propMap["properties"]; exists {
			if propertiesMap, ok := properties.(map[string]any); ok {
				propSchema.Properties = genaiConvertProperties(propertiesMap)
			}
		}
		// Handle required fields
		if required, exists := propMap["required"]; exists {
			if requiredSlice, ok := required.([]string); ok {
				propSchema.Required = requiredSlice
			}
		}
	}
}

// genaiConvertSingleProperty converts a single property definition
func genaiConvertSingleProperty(propMap map[string]any) *genai.Schema {
	propSchema := &genai.Schema{}
	if typeVal, exists := propMap["type"]; exists {
		if typeStr, ok := typeVal.(string); ok {
			switch typeStr {
			case "string":
				propSchema.Type = genai.TypeString
			case "number":
				propSchema.Type = genai.TypeNumber
			case "integer":
				propSchema.Type = genai.TypeInteger
			case "boolean":
				propSchema.Type = genai.TypeBoolean
			case "array":
				propSchema.Type = genai.TypeArray
			case "object":
				propSchema.Type = genai.TypeObject
				// Handle nested properties
				if properties, exists := propMap["properties"]; exists {
					if propertiesMap, ok := properties.(map[string]any); ok {
						propSchema.Properties = genaiConvertProperties(propertiesMap)
					}
				}
				// Handle required fields
				if required, exists := propMap["required"]; exists {
					if requiredSlice, ok := required.([]string); ok {
						propSchema.Required = requiredSlice
					}
				}
			}
		}
	}
	return propSchema
}
