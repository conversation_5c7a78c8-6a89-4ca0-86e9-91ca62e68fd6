package providers

import (
	"context"
	"time"
)

// Provider defines the interface for AI providers
type Provider interface {
	GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error)
	Name() string
	SupportedModels() []string
	GetModelInfo(ctx context.Context, modelName string) (*ModelInfo, error)
	Close() error
}

// GenerationRequest standardizes input across providers
type GenerationRequest struct {
	SystemPrompt string
	UserPrompt   string
	Schema       *ResponseSchema
	Config       *GenerationConfig
}

// GenerationConfig holds generation parameters
type GenerationConfig struct {
	Model       string
	Temperature float32
	MaxTokens   int
	Timeout     time.Duration
}

// GenerationResponse standardizes output across providers
type GenerationResponse struct {
	Content  string
	Metadata map[string]any
	Usage    *UsageStats
}

// ResponseSchema defines expected JSON structure
type ResponseSchema struct {
	Type       string         `json:"type"`
	Properties map[string]any `json:"properties"`
	Required   []string       `json:"required"`
}

// UsageStats tracks token usage
type UsageStats struct {
	PromptTokens     int
	CompletionTokens int
	TotalTokens      int
}

// ModelInfo contains information about a specific model
type ModelInfo struct {
	Name             string            `json:"name"`
	DisplayName      string            `json:"displayName"`
	Description      string            `json:"description"`
	Version          string            `json:"version"`
	InputTokenLimit  int32             `json:"inputTokenLimit"`
	OutputTokenLimit int32             `json:"outputTokenLimit"`
	SupportedActions []string          `json:"supportedActions"`
	Labels           map[string]string `json:"labels"`
	Provider         string            `json:"provider"`
}
