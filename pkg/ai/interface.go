package ai

import (
	"context"
	"resumatter/pkg/ai/providers"
	"resumatter/pkg/types"
)

type ClientInterface interface {
	TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error)
	EvaluateResume(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error)
	AnalyzeJob(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error)
	GetModelInfo(ctx context.Context, operation string) (*providers.ModelInfo, error)
	GetOperationModels(ctx context.Context) (map[string]*providers.ModelInfo, error)
	Close() error
}
