//go:build fast

package ai

import (
	"context"
	"errors"
	"testing"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/types"
)

// MockClient implements ClientInterface for testing
type MockClient struct {
	TailorResumeFunc       func(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error)
	EvaluateResumeFunc     func(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error)
	AnalyzeJobFunc         func(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error)
	GetModelInfoFunc       func(ctx context.Context, operation string) (*providers.ModelInfo, error)
	GetOperationModelsFunc func(ctx context.Context) (map[string]*providers.ModelInfo, error)
	CloseFunc              func() error
}

// Ensure MockClient implements ClientInterface
var _ ClientInterface = (*MockClient)(nil)

func (m *MockClient) TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
	if m.TailorResumeFunc != nil {
		return m.TailorResumeFunc(ctx, input)
	}
	return &types.TailorResumeOutput{}, nil
}

func (m *MockClient) EvaluateResume(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error) {
	if m.EvaluateResumeFunc != nil {
		return m.EvaluateResumeFunc(ctx, input)
	}
	return &types.EvaluateResumeOutput{}, nil
}

func (m *MockClient) AnalyzeJob(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error) {
	if m.AnalyzeJobFunc != nil {
		return m.AnalyzeJobFunc(ctx, input)
	}
	return &types.AnalyzeJobOutput{}, nil
}

func (m *MockClient) GetModelInfo(ctx context.Context, operation string) (*providers.ModelInfo, error) {
	if m.GetModelInfoFunc != nil {
		return m.GetModelInfoFunc(ctx, operation)
	}
	return &providers.ModelInfo{
		Name:        "mock-model",
		DisplayName: "Mock Model",
		Provider:    "mock",
	}, nil
}

func (m *MockClient) GetOperationModels(ctx context.Context) (map[string]*providers.ModelInfo, error) {
	if m.GetOperationModelsFunc != nil {
		return m.GetOperationModelsFunc(ctx)
	}
	return map[string]*providers.ModelInfo{
		"tailor": {
			Name:        "mock-tailor-model",
			DisplayName: "Mock Tailor Model",
			Provider:    "mock",
		},
	}, nil
}

func (m *MockClient) Close() error {
	if m.CloseFunc != nil {
		return m.CloseFunc()
	}
	return nil
}

// Test 1: Simple mock client functionality
func TestMockClient_Basic(t *testing.T) {
	mock := &MockClient{}

	// Test TailorResume with default behavior
	result, err := mock.TailorResume(context.Background(), types.TailorResumeInput{})
	if err != nil {
		t.Errorf("TailorResume() unexpected error = %v", err)
	}
	if result == nil {
		t.Error("TailorResume() should return non-nil result")
	}

	// Test Close with default behavior
	err = mock.Close()
	if err != nil {
		t.Errorf("Close() unexpected error = %v", err)
	}
}

// Test 2: Mock with custom function behavior
func TestMockClient_CustomBehavior(t *testing.T) {
	mock := &MockClient{
		TailorResumeFunc: func(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
			return &types.TailorResumeOutput{
				TailoredResume: "Custom tailored resume",
				ATSAnalysis: types.ATSAnalysis{
					Score:      85,
					Strengths:  "Good keywords",
					Weaknesses: "Could improve formatting",
				},
			}, nil
		},
	}

	input := types.TailorResumeInput{
		BaseResume:     "Original resume",
		JobDescription: "Job requirements",
	}

	result, err := mock.TailorResume(context.Background(), input)
	if err != nil {
		t.Errorf("TailorResume() unexpected error = %v", err)
	}

	if result.TailoredResume != "Custom tailored resume" {
		t.Errorf("TailorResume() TailoredResume = %v, want %v", result.TailoredResume, "Custom tailored resume")
	}

	if result.ATSAnalysis.Score != 85 {
		t.Errorf("TailorResume() ATS Score = %v, want %v", result.ATSAnalysis.Score, 85)
	}
}

// Test 3: Mock with error behavior
func TestMockClient_ErrorBehavior(t *testing.T) {
	mock := &MockClient{
		TailorResumeFunc: func(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
			return nil, errors.New("mock API error")
		},
	}

	input := types.TailorResumeInput{
		BaseResume:     "Test resume",
		JobDescription: "Test job",
	}

	result, err := mock.TailorResume(context.Background(), input)
	if err == nil {
		t.Error("TailorResume() expected error but got none")
	}

	if result != nil {
		t.Error("TailorResume() should return nil result on error")
	}

	if !contains(err.Error(), "mock API error") {
		t.Errorf("TailorResume() error = %v, want error containing 'mock API error'", err)
	}
}

// Test 4: Mock that captures input parameters
func TestMockClient_InputCapture(t *testing.T) {
	var capturedInput types.TailorResumeInput
	var capturedContext context.Context

	mock := &MockClient{
		TailorResumeFunc: func(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
			capturedContext = ctx
			capturedInput = input
			return &types.TailorResumeOutput{
				TailoredResume: "Processed: " + input.BaseResume,
			}, nil
		},
	}

	expectedInput := types.TailorResumeInput{
		BaseResume:     "Software Engineer with Go experience",
		JobDescription: "Looking for Go developer with 3+ years",
	}

	result, err := mock.TailorResume(context.Background(), expectedInput)
	if err != nil {
		t.Errorf("TailorResume() unexpected error = %v", err)
	}

	// Validate input was captured correctly
	if capturedInput.BaseResume != expectedInput.BaseResume {
		t.Errorf("Captured BaseResume = %v, want %v", capturedInput.BaseResume, expectedInput.BaseResume)
	}

	if capturedInput.JobDescription != expectedInput.JobDescription {
		t.Errorf("Captured JobDescription = %v, want %v", capturedInput.JobDescription, expectedInput.JobDescription)
	}

	// Validate context was passed
	if capturedContext == nil {
		t.Error("Context should not be nil")
	}

	// Validate result uses input data
	expectedResult := "Processed: " + expectedInput.BaseResume
	if result.TailoredResume != expectedResult {
		t.Errorf("TailoredResume = %v, want %v", result.TailoredResume, expectedResult)
	}
}

// Test 5: Mock EvaluateResume method
func TestMockClient_EvaluateResume(t *testing.T) {
	mock := &MockClient{
		EvaluateResumeFunc: func(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error) {
			// Simulate finding issues if resumes are different
			findings := []types.EvaluationFinding{}
			if input.BaseResume != input.TailoredResume {
				findings = append(findings, types.EvaluationFinding{
					Type:        "Modification",
					Description: "Resume content was changed",
					Evidence:    "Base and tailored versions differ",
				})
			}

			return &types.EvaluateResumeOutput{
				Summary:  "Evaluation completed",
				Findings: findings,
			}, nil
		},
	}

	input := types.EvaluateResumeInput{
		BaseResume:     "Original resume content",
		TailoredResume: "Modified resume content",
	}

	result, err := mock.EvaluateResume(context.Background(), input)
	if err != nil {
		t.Errorf("EvaluateResume() unexpected error = %v", err)
	}

	if result.Summary != "Evaluation completed" {
		t.Errorf("EvaluateResume() Summary = %v, want %v", result.Summary, "Evaluation completed")
	}

	if len(result.Findings) != 1 {
		t.Errorf("EvaluateResume() Findings count = %v, want %v", len(result.Findings), 1)
	}

	if len(result.Findings) > 0 && result.Findings[0].Type != "Modification" {
		t.Errorf("EvaluateResume() Finding Type = %v, want %v", result.Findings[0].Type, "Modification")
	}
}

// Test 6: Mock AnalyzeJob with complex nested structures
func TestMockClient_AnalyzeJob_Complex(t *testing.T) {
	mock := &MockClient{
		AnalyzeJobFunc: func(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error) {
			// Simulate analysis based on job description content
			score := 75
			flaggedTerms := []string{}

			if contains(input.JobDescription, "rockstar") {
				flaggedTerms = append(flaggedTerms, "rockstar")
				score -= 20
			}
			if contains(input.JobDescription, "ninja") {
				flaggedTerms = append(flaggedTerms, "ninja")
				score -= 20
			}

			return &types.AnalyzeJobOutput{
				JobQualityScore: score,
				Clarity: types.JobQualityScore{
					Score:        80,
					Analysis:     "Generally clear requirements",
					Improvements: []string{"Add specific technologies", "Include experience level"},
				},
				Inclusivity: types.InclusivityAnalysis{
					Score:        score,
					Analysis:     "Contains some problematic language",
					FlaggedTerms: flaggedTerms,
					Suggestions:  []string{"Use professional job titles", "Avoid buzzwords"},
				},
				CandidateAttraction: types.CandidateAttraction{
					Score:      70,
					Strengths:  []string{"Good benefits mentioned"},
					Weaknesses: []string{"Unclear growth path"},
				},
				MarketCompetitiveness: types.MarketCompetitiveness{
					SalaryTransparency:  "No salary range provided",
					RequirementsRealism: "Mostly realistic",
					IndustryAlignment:   "Well aligned",
				},
				Recommendations: []string{"Improve language", "Add salary range"},
			}, nil
		},
	}

	input := types.AnalyzeJobInput{
		JobDescription: "Looking for a rockstar ninja developer",
	}

	result, err := mock.AnalyzeJob(context.Background(), input)
	if err != nil {
		t.Errorf("AnalyzeJob() unexpected error = %v", err)
	}

	// Validate overall score was reduced due to problematic terms
	if result.JobQualityScore != 35 { // 75 - 20 - 20 = 35
		t.Errorf("AnalyzeJob() JobQualityScore = %v, want %v", result.JobQualityScore, 35)
	}

	// Validate flagged terms were detected
	expectedFlagged := []string{"rockstar", "ninja"}
	if len(result.Inclusivity.FlaggedTerms) != len(expectedFlagged) {
		t.Errorf("AnalyzeJob() FlaggedTerms count = %v, want %v", len(result.Inclusivity.FlaggedTerms), len(expectedFlagged))
	}

	// Validate nested structures
	if result.Clarity.Score != 80 {
		t.Errorf("AnalyzeJob() Clarity Score = %v, want %v", result.Clarity.Score, 80)
	}

	if len(result.Clarity.Improvements) != 2 {
		t.Errorf("AnalyzeJob() Improvements count = %v, want %v", len(result.Clarity.Improvements), 2)
	}

	if len(result.Recommendations) != 2 {
		t.Errorf("AnalyzeJob() Recommendations count = %v, want %v", len(result.Recommendations), 2)
	}
}
