//go:build fast

package ai

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/sony/gobreaker/v2"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// Test 1: Simple creation test
func TestNewAICircuitBreaker_Creation(t *testing.T) {
	log := logger.NewDefault()

	// Test with circuit breaker enabled
	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	if cb == nil {
		t.Fatal("NewAICircuitBreaker() returned nil when enabled")
	}

	if cb.cb == nil {
		t.Fatal("NewAICircuitBreaker() created circuit breaker with nil internal breaker")
	}

	if cb.logger == nil {
		t.Fatal("NewAICircuitBreaker() created circuit breaker with nil logger")
	}

	// Verify the circuit breaker name
	expectedName := "AI-tailor"
	if cb.cb.Name() != expectedName {
		t.Errorf("Circuit breaker name = %s, want %s", cb.cb.Name(), expectedName)
	}

	// Verify initial state is closed
	if cb.cb.State() != gobreaker.StateClosed {
		t.Errorf("Initial circuit breaker state = %s, want %s", cb.cb.State(), gobreaker.StateClosed)
	}
}

// Test 2: Test with circuit breaker disabled
func TestNewAICircuitBreaker_Disabled(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled: false, // Disabled
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	if cb != nil {
		t.Fatal("NewAICircuitBreaker() should return nil when disabled")
	}
}

// Test 3: Test successful execution with enabled circuit breaker
func TestAICircuitBreaker_Execute_Success(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Mock successful response
	expectedResponse := &providers.GenerationResponse{}

	result, err := cb.Execute(func() (*providers.GenerationResponse, error) {
		return expectedResponse, nil
	})

	if err != nil {
		t.Errorf("Execute() with successful function returned error: %v", err)
	}

	if result != expectedResponse {
		t.Errorf("Execute() returned wrong response")
	}

	// Circuit breaker should still be closed
	if cb.cb.State() != gobreaker.StateClosed {
		t.Errorf("Circuit breaker state after success = %s, want %s", cb.cb.State(), gobreaker.StateClosed)
	}
}

// Test 4: Test execution with disabled circuit breaker (nil case)
func TestAICircuitBreaker_Execute_Disabled(t *testing.T) {
	// Test with nil circuit breaker (disabled case)
	var cb *AICircuitBreaker = nil

	expectedResponse := &providers.GenerationResponse{}

	result, err := cb.Execute(func() (*providers.GenerationResponse, error) {
		return expectedResponse, nil
	})

	if err != nil {
		t.Errorf("Execute() with disabled circuit breaker returned error: %v", err)
	}

	if result != expectedResponse {
		t.Errorf("Execute() with disabled circuit breaker returned wrong response")
	}
}

// Test 5: Test execution with function error
func TestAICircuitBreaker_Execute_FunctionError(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	expectedError := errors.New("AI API error")

	result, err := cb.Execute(func() (*providers.GenerationResponse, error) {
		return nil, expectedError
	})

	if err != expectedError {
		t.Errorf("Execute() with failing function returned wrong error: got %v, want %v", err, expectedError)
	}

	if result != nil {
		t.Errorf("Execute() with failing function returned non-nil result")
	}

	// Circuit breaker should still be closed (not enough failures yet)
	if cb.cb.State() != gobreaker.StateClosed {
		t.Errorf("Circuit breaker state after single failure = %s, want %s", cb.cb.State(), gobreaker.StateClosed)
	}
}

// Test 6: Test GetStats with enabled circuit breaker
func TestAICircuitBreaker_GetStats_Enabled(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	stats := cb.GetStats()

	if stats == nil {
		t.Fatal("GetStats() returned nil")
	}

	// Check required fields
	if enabled, ok := stats["enabled"].(bool); !ok || !enabled {
		t.Errorf("GetStats() enabled = %v, want true", stats["enabled"])
	}

	if name, ok := stats["name"].(string); !ok || name != "AI-tailor" {
		t.Errorf("GetStats() name = %v, want 'AI-tailor'", stats["name"])
	}

	if state, ok := stats["state"].(string); !ok || state != "closed" {
		t.Errorf("GetStats() state = %v, want 'closed'", stats["state"])
	}

	if _, ok := stats["counts"]; !ok {
		t.Errorf("GetStats() missing 'counts' field")
	}
}

// Test 7: Test GetStats with disabled circuit breaker
func TestAICircuitBreaker_GetStats_Disabled(t *testing.T) {
	var cb *AICircuitBreaker = nil

	stats := cb.GetStats()

	if stats == nil {
		t.Fatal("GetStats() returned nil for disabled circuit breaker")
	}

	if enabled, ok := stats["enabled"].(bool); !ok || enabled {
		t.Errorf("GetStats() for disabled circuit breaker enabled = %v, want false", stats["enabled"])
	}

	// Should only have enabled field when disabled
	if len(stats) != 1 {
		t.Errorf("GetStats() for disabled circuit breaker returned %d fields, want 1", len(stats))
	}
}

// Test 8: Test IsHealthy method
func TestAICircuitBreaker_IsHealthy(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Initially should be healthy (closed state)
	if !cb.IsHealthy() {
		t.Errorf("IsHealthy() = false, want true for initial closed state")
	}

	// Test with disabled circuit breaker
	var disabledCB *AICircuitBreaker = nil
	if !disabledCB.IsHealthy() {
		t.Errorf("IsHealthy() = false, want true for disabled circuit breaker")
	}
}

// Test 9: Test GetState method
func TestAICircuitBreaker_GetState(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Initially should be closed
	if state := cb.GetState(); state != "closed" {
		t.Errorf("GetState() = %s, want 'closed'", state)
	}

	// Test with disabled circuit breaker
	var disabledCB *AICircuitBreaker = nil
	if state := disabledCB.GetState(); state != "disabled" {
		t.Errorf("GetState() for disabled circuit breaker = %s, want 'disabled'", state)
	}
}

// Test 10: Test GetCounts method
func TestAICircuitBreaker_GetCounts(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Initially should have zero counts
	counts := cb.GetCounts()
	if counts.Requests != 0 {
		t.Errorf("GetCounts() initial Requests = %d, want 0", counts.Requests)
	}
	if counts.TotalSuccesses != 0 {
		t.Errorf("GetCounts() initial TotalSuccesses = %d, want 0", counts.TotalSuccesses)
	}
	if counts.TotalFailures != 0 {
		t.Errorf("GetCounts() initial TotalFailures = %d, want 0", counts.TotalFailures)
	}

	// Test with disabled circuit breaker
	var disabledCB *AICircuitBreaker = nil
	disabledCounts := disabledCB.GetCounts()
	// Should return empty counts struct
	if disabledCounts.Requests != 0 {
		t.Errorf("GetCounts() for disabled circuit breaker Requests = %d, want 0", disabledCounts.Requests)
	}
}

// Test 11: Test circuit breaker opening after threshold failures
func TestAICircuitBreaker_OpenOnFailures(t *testing.T) {
	log := logger.NewDefault()

	// Configure with low thresholds for easier testing
	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6, // 60% failure rate
		CircuitBreakerMinRequests:      3,   // Only need 3 requests minimum
		CircuitBreakerMaxRequests:      2,   // 2 requests in half-open
		CircuitBreakerInterval:         1 * time.Second,
		CircuitBreakerTimeout:          100 * time.Millisecond, // Short timeout for testing
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Execute 3 requests: 1 success, 2 failures = 66% failure rate (above 60% threshold)

	// First request: success
	_, err := cb.Execute(func() (*providers.GenerationResponse, error) {
		return &providers.GenerationResponse{}, nil
	})
	if err != nil {
		t.Errorf("First request failed unexpectedly: %v", err)
	}

	// Second request: failure
	_, err = cb.Execute(func() (*providers.GenerationResponse, error) {
		return nil, errors.New("API error 1")
	})
	if err == nil {
		t.Error("Second request should have failed")
	}

	// Third request: failure (this should trigger the circuit breaker to open)
	_, err = cb.Execute(func() (*providers.GenerationResponse, error) {
		return nil, errors.New("API error 2")
	})
	if err == nil {
		t.Error("Third request should have failed")
	}

	// Check that circuit breaker is now open
	if cb.cb.State() != gobreaker.StateOpen {
		t.Errorf("Circuit breaker state after failures = %s, want %s", cb.cb.State(), gobreaker.StateOpen)
	}

	// Note: When circuit breaker opens, the counts may be reset depending on the implementation
	// We'll verify that the circuit breaker opened, which is the main behavior we're testing
	counts := cb.GetCounts()
	t.Logf("Circuit breaker counts after opening: Requests=%d, Successes=%d, Failures=%d",
		counts.Requests, counts.TotalSuccesses, counts.TotalFailures)

	// Circuit breaker should not be healthy when open
	if cb.IsHealthy() {
		t.Error("Circuit breaker should not be healthy when open")
	}

	// State should be "open"
	if state := cb.GetState(); state != "open" {
		t.Errorf("GetState() = %s, want 'open'", state)
	}
}

// Test 12: Test fast failure when circuit breaker is open
func TestAICircuitBreaker_FastFailWhenOpen(t *testing.T) {
	log := logger.NewDefault()

	// Configure with very low thresholds
	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.5, // 50% failure rate
		CircuitBreakerMinRequests:      2,   // Only need 2 requests minimum
		CircuitBreakerMaxRequests:      1,
		CircuitBreakerInterval:         1 * time.Second,
		CircuitBreakerTimeout:          100 * time.Millisecond,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Force circuit breaker to open by causing failures
	for i := 0; i < 2; i++ {
		cb.Execute(func() (*providers.GenerationResponse, error) {
			return nil, errors.New("forced failure")
		})
	}

	// Verify circuit breaker is open
	if cb.cb.State() != gobreaker.StateOpen {
		t.Fatalf("Circuit breaker should be open, got %s", cb.cb.State())
	}

	// Now test that subsequent calls fail fast
	callExecuted := false
	_, err := cb.Execute(func() (*providers.GenerationResponse, error) {
		callExecuted = true
		return &providers.GenerationResponse{}, nil
	})

	// Should get a circuit breaker error, not execute the function
	if err == nil {
		t.Error("Expected error when circuit breaker is open")
	}

	if callExecuted {
		t.Error("Function should not have been executed when circuit breaker is open")
	}

	// Error should be from gobreaker
	if !errors.Is(err, gobreaker.ErrOpenState) {
		t.Errorf("Expected gobreaker.ErrOpenState, got %v", err)
	}
}

// Test 13: Test different operation types get different circuit breaker names
func TestAICircuitBreaker_DifferentOperationTypes(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	// Test different operation types
	operations := []config.OperationType{
		config.OperationTailor,
		config.OperationEvaluate,
		config.OperationAnalyze,
	}

	expectedNames := []string{
		"AI-tailor",
		"AI-evaluate",
		"AI-analyze",
	}

	for i, op := range operations {
		cb := NewAICircuitBreaker(op, cfg, log, nil)
		if cb.cb.Name() != expectedNames[i] {
			t.Errorf("Circuit breaker for %s has name %s, want %s",
				op, cb.cb.Name(), expectedNames[i])
		}
	}
}

// Test 14: Test half-open state transition and recovery
func TestAICircuitBreaker_HalfOpenRecovery(t *testing.T) {
	log := logger.NewDefault()

	// Configure with very short timeout for testing
	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.5, // 50% failure rate
		CircuitBreakerMinRequests:      2,   // Only need 2 requests minimum
		CircuitBreakerMaxRequests:      1,   // Only 1 request in half-open
		CircuitBreakerInterval:         1 * time.Second,
		CircuitBreakerTimeout:          50 * time.Millisecond, // Very short timeout
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Force circuit breaker to open
	for i := 0; i < 2; i++ {
		cb.Execute(func() (*providers.GenerationResponse, error) {
			return nil, errors.New("forced failure")
		})
	}

	// Verify it's open
	if cb.cb.State() != gobreaker.StateOpen {
		t.Fatalf("Circuit breaker should be open, got %s", cb.cb.State())
	}

	// Wait for timeout to transition to half-open
	time.Sleep(60 * time.Millisecond)

	// Next call should transition to half-open and execute
	callExecuted := false
	_, err := cb.Execute(func() (*providers.GenerationResponse, error) {
		callExecuted = true
		return &providers.GenerationResponse{}, nil // Success
	})

	if err != nil {
		t.Errorf("Half-open request failed: %v", err)
	}

	if !callExecuted {
		t.Error("Function should have been executed in half-open state")
	}

	// After successful half-open request, should be closed again
	if cb.cb.State() != gobreaker.StateClosed {
		t.Errorf("Circuit breaker should be closed after successful half-open, got %s", cb.cb.State())
	}

	// Should be healthy again
	if !cb.IsHealthy() {
		t.Error("Circuit breaker should be healthy after recovery")
	}
}

// Test 15: Test state change logging (verify OnStateChange callback)
func TestAICircuitBreaker_StateChangeLogging(t *testing.T) {
	// Create a mock logger that captures log calls
	mockLog := &mockLogger{logs: make([]logEntry, 0)}

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.5,
		CircuitBreakerMinRequests:      2,
		CircuitBreakerMaxRequests:      1,
		CircuitBreakerInterval:         1 * time.Second,
		CircuitBreakerTimeout:          50 * time.Millisecond,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, mockLog, nil)

	// Force circuit breaker to open (this should trigger state change logging)
	for i := 0; i < 2; i++ {
		cb.Execute(func() (*providers.GenerationResponse, error) {
			return nil, errors.New("forced failure")
		})
	}

	// Check that state change was logged
	found := false
	for _, entry := range mockLog.logs {
		if entry.message == "Circuit breaker state changed" {
			found = true

			// Verify log fields
			if entry.fields["name"] != "AI-tailor" {
				t.Errorf("Expected name 'AI-tailor', got %v", entry.fields["name"])
			}
			if entry.fields["operation_type"] != "tailor" {
				t.Errorf("Expected operation_type 'tailor', got %v", entry.fields["operation_type"])
			}
			if entry.fields["from"] != "closed" {
				t.Errorf("Expected from 'closed', got %v", entry.fields["from"])
			}
			if entry.fields["to"] != "open" {
				t.Errorf("Expected to 'open', got %v", entry.fields["to"])
			}
			break
		}
	}

	if !found {
		t.Error("State change log entry not found")
	}
}

// Test 16: Test execution with success and failure tracking
func TestAICircuitBreaker_ExecutionTracking(t *testing.T) {
	log := logger.NewDefault()

	cfg := &config.ResolvedOperationConfig{
		CircuitBreakerEnabled:          true,
		CircuitBreakerFailureThreshold: 0.6,
		CircuitBreakerMinRequests:      5,
		CircuitBreakerMaxRequests:      3,
		CircuitBreakerInterval:         60 * time.Second,
		CircuitBreakerTimeout:          30 * time.Second,
	}

	cb := NewAICircuitBreaker(config.OperationTailor, cfg, log, nil)

	// Execute a mix of successful and failed requests
	successCount := 0
	failureCount := 0

	for i := 0; i < 10; i++ {
		_, err := cb.Execute(func() (*providers.GenerationResponse, error) {
			if i%3 == 0 { // Every 3rd request fails
				return nil, errors.New("simulated failure")
			}
			return &providers.GenerationResponse{}, nil
		})

		if err != nil {
			failureCount++
		} else {
			successCount++
		}
	}

	// Verify counts match our expectations
	// Pattern: i%3 == 0 means positions 0, 3, 6, 9 fail = 4 failures, 6 successes
	if successCount != 6 {
		t.Errorf("Expected 6 successes, got %d", successCount)
	}
	if failureCount != 4 {
		t.Errorf("Expected 4 failures, got %d", failureCount)
	}

	// Verify circuit breaker counts
	counts := cb.GetCounts()
	if counts.Requests != 10 {
		t.Errorf("Circuit breaker requests = %d, want 10", counts.Requests)
	}
	if counts.TotalSuccesses != 6 {
		t.Errorf("Circuit breaker successes = %d, want 6", counts.TotalSuccesses)
	}
	if counts.TotalFailures != 4 {
		t.Errorf("Circuit breaker failures = %d, want 4", counts.TotalFailures)
	}

	// Should still be closed (30% failure rate < 60% threshold)
	if cb.cb.State() != gobreaker.StateClosed {
		t.Errorf("Circuit breaker should still be closed, got %s", cb.cb.State())
	}
}

// Mock logger for testing state change callbacks
type logEntry struct {
	message string
	fields  map[string]any
}

type mockLogger struct {
	logs []logEntry
}

func (m *mockLogger) Debug(ctx context.Context, msg string, fields ...logger.Field) {
	m.addLog(msg, fields)
}

func (m *mockLogger) Info(ctx context.Context, msg string, fields ...logger.Field) {
	m.addLog(msg, fields)
}

func (m *mockLogger) Warn(ctx context.Context, msg string, fields ...logger.Field) {
	m.addLog(msg, fields)
}

func (m *mockLogger) Error(ctx context.Context, msg string, fields ...logger.Field) {
	m.addLog(msg, fields)
}

func (m *mockLogger) ErrorWithErr(ctx context.Context, msg string, err error, fields ...logger.Field) {
	allFields := append(fields, logger.String("error", err.Error()))
	m.addLog(msg, allFields)
}

func (m *mockLogger) SetLevel(level logger.Level) {
	// Mock implementation - do nothing
}

func (m *mockLogger) GetLevel() logger.Level {
	return logger.LevelInfo
}

func (m *mockLogger) With(fields ...logger.Field) logger.Logger {
	return m
}

func (m *mockLogger) Named(name string) logger.Logger {
	return m
}

func (m *mockLogger) addLog(msg string, fields []logger.Field) {
	fieldMap := make(map[string]any)
	for _, field := range fields {
		fieldMap[field.Key] = field.Value
	}
	m.logs = append(m.logs, logEntry{
		message: msg,
		fields:  fieldMap,
	})
}
