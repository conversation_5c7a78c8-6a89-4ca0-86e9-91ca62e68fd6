# Resumatter Multi-Provider Configuration Example
# Copy this file to config.yaml and customize for your environment

# AI Configuration
ai:
  # Global defaults (fallback for operations without specific settings)
  default_provider: "genai-gemini"
  default_model: "gemini-2.0-flash-lite"
  default_temperature: 0.7
  default_timeout: "30s"

  # Per-operation configurations
  operations:
    # Resume tailoring - uses creative model with higher temperature
    tailor:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.7
      max_tokens: 8192
      timeout: "30s"
      api_key: "${GEMINI_API_KEY}"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"

    # Resume evaluation - uses Vertex AI for enterprise reliability
    evaluate:
      provider: "genai-vertexai"
      model: "gemini-2.5-pro"
      temperature: 0.3
      max_tokens: 8192
      timeout: "30s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"

    # Job analysis - balanced settings for analysis
    analyze:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.5
      max_tokens: 8192
      timeout: "30s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"

# Provider-specific configurations
providers:
  # GenAI configuration (used by both genai-gemini and genai-vertexai providers)
  genai:
    google_cloud_project: "${GOOGLE_CLOUD_PROJECT}"
    google_cloud_region: "us-central1"

# HTTP Server Configuration
server:
  port: "8080"
  environment: "development"

# Logging Configuration
logging:
  level: "info"
  format: "text"

# Observability Configuration
observability:
  tracing_enabled: false
  service_name: "resumatter"
  service_version: "dev"

# Auth Configuration
# Add API keys for HTTP authentication
# Example:
auth:
  enabled: false
  logging_only: true
  api_keys:
    - "sk-rsm-key-1"
    - "sk-rsm-key-2"

# Rate Limiting Configuration
# Controls request rate limits per operation and client
rate_limit:
  enabled: false                    # Enable/disable rate limiting
  backend: "memory"                 # "memory" for development, "redis" for production
  strategy: "token_bucket"          # "token_bucket", "fixed_window"
  key_by: "ip"                      # "ip", "api_key", "header"
  header_name: "X-Client-ID"        # Custom header name when key_by is "header"
  
  # Default rate limits (applied when operation-specific limits not set)
  defaults:
    requests_per_minute: 60
    requests_per_hour: 1000
    requests_per_day: 10000
    burst_size: 10
  
  # Per-operation rate limits (AI operations have lower limits due to cost)
  operations:
    tailor:
      enabled: true
      requests_per_minute: 10       # AI operations are expensive
      requests_per_hour: 100
      requests_per_day: 1000
      burst_size: 5
    
    evaluate:
      enabled: true
      requests_per_minute: 15
      requests_per_hour: 150
      requests_per_day: 1500
      burst_size: 5
    
    analyze:
      enabled: true
      requests_per_minute: 20       # Lighter operation, higher limits
      requests_per_hour: 200
      requests_per_day: 2000
      burst_size: 8
  
  # Redis configuration (only used when backend is "redis")
  redis:
    address: "localhost:6379"
    password: "${REDIS_PASSWORD}"
    db: 0
    pool_size: 10

# Environment Variable Examples:
# GEMINI_API_KEY=your_gemini_api_key
# GOOGLE_CLOUD_PROJECT=your-gcp-project
# GOOGLE_CLOUD_REGION=us-central1
# PORT=8080
# ENVIRONMENT=production
# LOG_LEVEL=info
# OTEL_TRACING_ENABLED=true

# Rate Limiting Environment Variables:
# RESUMATTER_RATE_LIMIT_ENABLED=true
# RESUMATTER_RATE_LIMIT_BACKEND=redis
# RESUMATTER_RATE_LIMIT_STRATEGY=fixed_window
# RESUMATTER_RATE_LIMIT_KEY_BY=api_key
# RESUMATTER_RATE_LIMIT_HEADER_NAME=X-Client-ID
# RESUMATTER_RATE_LIMIT_DEFAULTS_REQUESTS_PER_MINUTE=60
# RESUMATTER_RATE_LIMIT_OPERATIONS_TAILOR_REQUESTS_PER_MINUTE=5
# RESUMATTER_RATE_LIMIT_OPERATIONS_EVALUATE_REQUESTS_PER_MINUTE=8
# RESUMATTER_RATE_LIMIT_OPERATIONS_ANALYZE_REQUESTS_PER_MINUTE=12
# Redis (standard environment variables):
# REDIS_ADDRESS=redis:6379
# REDIS_PASSWORD=your_redis_password
# REDIS_DB=1
# REDIS_POOL_SIZE=10