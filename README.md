# Resumatter

An AI-powered HTTP service for resume optimization and job analysis. Built on Google's GenAI platform with configurable per-operation models, circuit breaker protection, structured logging, and OpenTelemetry tracing.

## Features

- **Resume tailoring**: Adapt resume content for specific job postings using AI
- **Resume evaluation**: Check consistency and quality between different resume versions
- **Job analysis**: Review job descriptions for clarity, completeness, and effectiveness
- **Google GenAI support**: Gemini API and Vertex AI with configurable models per operation
- **Per-operation configuration**: Different models, temperatures, and settings for each AI operation
- **Circuit breaker protection**: Automatic failover and recovery for AI service reliability
- **Observability**: Structured logging and OpenTelemetry tracing support
- **RESTful API**: JSON-based HTTP endpoints for easy integration

## Requirements

- Go 1.24.5 or later
- Google Gemini API key or Google Cloud Project (for Vertex AI)

## Quick Start

1. **Get the code and build:**
   ```bash
   git clone <repository>
   cd resumatter
   make build
   ```

2. **Configure the service:**
   ```bash
   # Copy example configuration
   cp config.example.yaml config.yaml

   # Set your API key
   export GEMINI_API_KEY="your-api-key"
   # OR for Vertex AI
   export GOOGLE_CLOUD_PROJECT="your-project-id"
   ```

3. **Start the server:**
   ```bash
   ./build/resumatter
   # Server starts on http://localhost:8080
   ```

## API Endpoints

- `POST /api/v1/tailor` - Optimize resume for specific job posting
- `POST /api/v1/evaluate` - Check resume consistency and accuracy
- `POST /api/v1/analyze` - Analyze job description quality
- `GET /health` - Health check
- `GET /ready` - Readiness check

See [USAGE.md](USAGE.md) for detailed API documentation and examples.

## Configuration

The service uses YAML configuration with environment variable support. Key features:

- **Per-operation AI settings**: Different models, temperatures, and parameters for each operation
- **Google GenAI providers**: Gemini API (direct) and Vertex AI (Google Cloud)
- **Circuit Breakers**: Automatic failure protection and recovery per operation
- **Observability**: Structured logging and OpenTelemetry tracing

See `config.example.yaml` for complete configuration options.

## Getting API Keys

- **Google Gemini API**: Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Google Vertex AI**: Set up a [Google Cloud Project](https://cloud.google.com/vertex-ai/docs/start/cloud-environment)

## Development

```bash
# Run tests
make test

# Lint code
make lint

# Format code
make fmt
```

## License

Apache License 2.0 - see [LICENSE](LICENSE) file.
