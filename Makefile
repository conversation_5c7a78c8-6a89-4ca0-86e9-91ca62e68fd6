.PHONY: build clean test run-example help lint fmt vet

# Build configuration
BINARY_NAME = resumatter
BUILD_DIR = ./build
CMD_DIR = ./cmd/resumatter

# Shell function for API key check
check_api_key = \
	if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Error: Please set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable"; \
		exit 1; \
	fi

# Default target
all: build

# Build the application
build: deps
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(CMD_DIR)
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -f coverage.*
	rm -f *.out
	go clean

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod tidy

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...

# Lint code (requires golangci-lint)
lint:
	@echo "Linting code..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found, skipping lint check"; \
	fi

# Run fast unit tests (no API calls)
test-fast:
	@echo "Running fast unit tests..."
	go test -tags=fast -v ./...

# Run integration tests (requires API key)
test-integration:
	@echo "Running integration tests..."
	@if [ -z "$$GEMINI_API_KEY" ] && [ -z "$$RESUMATTER_AI_APIKEY" ]; then \
		echo "Warning: No API key set. Integration tests may be skipped."; \
	fi
	go test -tags=integration -v ./...

# Run all tests (fast + integration)
test:
	@echo "Running all tests..."
	@$(MAKE) test-fast
	@$(MAKE) test-integration

# Run fast tests with coverage
test-coverage: deps
	@echo "Running fast tests with coverage..."
	go test -tags=fast -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.out, coverage.html"

# Run integration tests with coverage
test-coverage-integration: deps
	@echo "Running integration tests with coverage..."
	go test -tags=integration -v -coverprofile=coverage-integration.out ./...
	go tool cover -html=coverage-integration.out -o coverage-integration.html
	@echo "Integration coverage report generated: coverage-integration.out, coverage-integration.html"

# Start the server (requires API key)
run-server-30s:
	@echo "Starting Resumatter HTTP server..."
	@$(check_api_key)
	timeout 30s go run $(CMD_DIR) serve --port 8080

# Run example tailor API call (requires server to be running)
run-example:
	@echo "Running example tailor API call..."
	@echo "Note: Make sure server is running with 'make run-server' in another terminal"
	@curl -X POST http://localhost:8080/api/v1/tailor \
		-H "Content-Type: application/json" \
		-d '{"baseResume": "'$$(cat examples/resume.txt | tr '\n' ' ' | sed 's/"/\\"/g')'", "jobDescription": "'$$(cat examples/job.txt | tr '\n' ' ' | sed 's/"/\\"/g')'"}' \
		| jq '.' || echo "Error: Server may not be running or jq not installed"

# Run example with JSON output (same as run-example now)
run-example-json:
	@echo "Running example with JSON output..."
	@echo "Note: Make sure server is running with 'make run-server' in another terminal"
	@curl -X POST http://localhost:8080/api/v1/tailor \
		-H "Content-Type: application/json" \
		-d '{"baseResume": "'$$(cat examples/resume.txt | tr '\n' ' ' | sed 's/"/\\"/g')'", "jobDescription": "'$$(cat examples/job.txt | tr '\n' ' ' | sed 's/"/\\"/g')'"}' \
		| jq '.'

# Run evaluation example (requires server to be running)
run-evaluate:
	@echo "Running evaluation example..."
	@echo "Note: Make sure server is running with 'make run-server' in another terminal"
	@curl -X POST http://localhost:8080/api/v1/evaluate \
		-H "Content-Type: application/json" \
		-d '{"baseResume": "'$$(cat examples/resume.txt | tr '\n' ' ' | sed 's/"/\\"/g')'", "tailoredResume": "'$$(cat examples/resume.txt | tr '\n' ' ' | sed 's/"/\\"/g')'"}' \
		| jq '.' || echo "Error: Server may not be running or jq not installed"

# Run analysis example (requires server to be running)
run-analyze:
	@echo "Running analysis example..."
	@echo "Note: Make sure server is running with 'make run-server' in another terminal"
	@curl -X POST http://localhost:8080/api/v1/analyze \
		-H "Content-Type: application/json" \
		-d '{"jobDescription": "'$$(cat examples/job.txt | tr '\n' ' ' | sed 's/"/\\"/g')'"}' \
		| jq '.' || echo "Error: Server may not be running or jq not installed"

# Test all API endpoints (requires server to be running)
test-api:
	@echo "Testing all API endpoints..."
	@echo "Note: Make sure server is running with 'make run-server' in another terminal"
	@./tmp_rovodev_test_api.sh

# Start server and run all tests in sequence
run-full-demo:
	@echo "Starting full demo (server + API tests)..."
	@$(check_api_key)
	@echo "Starting server in background..."
	@go run $(CMD_DIR) serve --port 8080 & \
	SERVER_PID=$$!; \
	echo "Server PID: $$SERVER_PID"; \
	sleep 3; \
	echo "Running API tests..."; \
	$(MAKE) test-api || true; \
	echo "Stopping server..."; \
	kill $$SERVER_PID 2>/dev/null || true; \
	wait $$SERVER_PID 2>/dev/null || true; \
	echo "Demo complete!"

# Install the binary to $GOPATH/bin
install: build
	@echo "Installing $(BINARY_NAME) to $$GOPATH/bin..."
	cp $(BUILD_DIR)/$(BINARY_NAME) $$GOPATH/bin/

# Development workflow (fast tests only for quick feedback)
dev: fmt vet lint test-fast

# Show help
help:
	@echo "Available targets:"
	@echo "  build                    - Build the application"
	@echo "  clean                    - Clean build artifacts"
	@echo "  deps                     - Download and tidy dependencies"
	@echo "  fmt                      - Format code"
	@echo "  vet                      - Vet code"
	@echo "  lint                     - Lint code (requires golangci-lint)"
	@echo "  test                     - Run all tests (fast + integration)"
	@echo "  test-fast                - Run fast unit tests (no API calls)"
	@echo "  test-integration         - Run integration tests (requires API key)"
	@echo "  test-coverage            - Run fast tests with coverage"
	@echo "  test-coverage-integration - Run integration tests with coverage"
	@echo "  run-server               - Start the HTTP server"
	@echo "  run-example              - Run example tailor API call"
	@echo "  run-example-json         - Run example with JSON output"
	@echo "  run-evaluate             - Run evaluation example"
	@echo "  run-analyze              - Run analysis example"
	@echo "  test-api                 - Test all API endpoints (requires server running)"
	@echo "  run-full-demo            - Start server and run complete API demo"
	@echo "  install                  - Install binary to GOPATH/bin"
	@echo "  dev                      - Run development workflow (fmt, vet, lint, test-fast)"
	@echo "  help                     - Show this help message"
	@echo ""
	@echo "Testing Strategy:"
	@echo "  test-fast        - Quick unit tests, no external dependencies"
	@echo "  test-integration - Slower tests that require valid API key"
	@echo ""
	@echo "Usage Examples:"
	@echo "  make run-server          - Start server on port 8080"
	@echo "  make test-api            - Test all endpoints (server must be running)"
	@echo "  make run-full-demo       - Complete demo (starts server, runs tests, stops server)"
	@echo ""
	@echo "Environment variables:"
	@echo "  GEMINI_API_KEY or RESUMATTER_AI_APIKEY - Required for API access"
	@echo "  GEMINI_MODEL - Optional, defaults to gemini-2.0-flash-lite"
	@echo ""
	@echo "Dependencies:"
	@echo "  curl - Required for API testing"
	@echo "  jq   - Required for JSON formatting (install with: brew install jq)"

