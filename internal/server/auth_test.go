//go:build fast

package server

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/service"
)

// TestServer_Authentication_Disabled tests that requests pass through when auth is disabled
func TestServer_Authentication_Disabled(t *testing.T) {
	server := createTestServerWithAuth(t, false, false, []string{})

	endpoints := []struct {
		method string
		path   string
	}{
		{"GET", "/health"},
		{"GET", "/ready"},
		{"POST", "/api/v1/tailor"},
		{"POST", "/api/v1/evaluate"},
		{"POST", "/api/v1/analyze"},
	}

	for _, endpoint := range endpoints {
		t.Run(endpoint.method+"_"+endpoint.path, func(t *testing.T) {
			var req *http.Request
			if endpoint.method == "POST" {
				// Send empty JSON for POST requests to avoid JSON parsing errors
				req = httptest.NewRequest(endpoint.method, endpoint.path, bytes.NewBufferString("{}"))
				req.Header.Set("Content-Type", "application/json")
			} else {
				req = httptest.NewRequest(endpoint.method, endpoint.path, nil)
			}

			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			// Should not get 401 (unauthorized) when auth is disabled
			if w.Code == http.StatusUnauthorized {
				t.Errorf("Got 401 Unauthorized when auth is disabled for %s %s",
					endpoint.method, endpoint.path)
			}
		})
	}
}

// TestServer_Authentication_ConfigValidation tests that invalid auth config prevents server creation
func TestServer_Authentication_ConfigValidation(t *testing.T) {
	// Test that auth enabled with no keys fails validation
	cfg := createAuthTestConfig(true, false, []string{})

	_, err := service.New(service.WithConfig(cfg))
	if err == nil {
		t.Fatal("Expected error when creating service with auth enabled but no keys configured")
	}

	expectedErrorSubstring := "authentication is enabled but no api keys are configured"
	if !contains(err.Error(), expectedErrorSubstring) {
		t.Errorf("Expected error to contain '%s', got: %s", expectedErrorSubstring, err.Error())
	}
}

// TestServer_Authentication_ValidKeys tests various valid API key formats
func TestServer_Authentication_ValidKeys(t *testing.T) {
	validKeys := []string{"test-key-1", "test-key-2"}
	server := createTestServerWithAuth(t, true, false, validKeys)

	tests := []struct {
		name   string
		header string
		desc   string
	}{
		{
			name:   "Bearer format",
			header: "Bearer test-key-1",
			desc:   "Standard Bearer token format",
		},
		{
			name:   "Api-Key format",
			header: "Api-Key test-key-2",
			desc:   "Standard Api-Key format",
		},
		{
			name:   "Direct key",
			header: "test-key-1",
			desc:   "Direct key without prefix",
		},
		{
			name:   "Bearer with spaces",
			header: "Bearer   test-key-2   ",
			desc:   "Bearer with extra whitespace",
		},
		{
			name:   "Api-Key with spaces",
			header: "Api-Key   test-key-1   ",
			desc:   "Api-Key with extra whitespace",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/health", nil)
			req.Header.Set("Authorization", tt.header)
			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			if w.Code != http.StatusOK {
				t.Errorf("%s: Expected status 200, got %d for header '%s'",
					tt.desc, w.Code, tt.header)
			}
		})
	}
}

// TestServer_Authentication_InvalidKeys tests various invalid API key scenarios
func TestServer_Authentication_InvalidKeys(t *testing.T) {
	validKeys := []string{"valid-key"}
	server := createTestServerWithAuth(t, true, false, validKeys)

	tests := []struct {
		name   string
		header string
		desc   string
	}{
		{
			name:   "No header",
			header: "",
			desc:   "Missing Authorization header",
		},
		{
			name:   "Invalid key",
			header: "invalid-key",
			desc:   "Wrong API key",
		},
		{
			name:   "Bearer invalid",
			header: "Bearer invalid-key",
			desc:   "Bearer with wrong key",
		},
		{
			name:   "Api-Key invalid",
			header: "Api-Key invalid-key",
			desc:   "Api-Key with wrong key",
		},
		{
			name:   "Wrong prefix",
			header: "Basic valid-key",
			desc:   "Wrong authentication scheme",
		},
		{
			name:   "Empty Bearer",
			header: "Bearer ",
			desc:   "Bearer with no key",
		},
		{
			name:   "Empty Api-Key",
			header: "Api-Key ",
			desc:   "Api-Key with no key",
		},
		{
			name:   "Case sensitive",
			header: "VALID-KEY",
			desc:   "Wrong case for key",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/health", nil)
			if tt.header != "" {
				req.Header.Set("Authorization", tt.header)
			}
			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			if w.Code != http.StatusUnauthorized {
				t.Errorf("%s: Expected status 401, got %d for header '%s'",
					tt.desc, w.Code, tt.header)
			}

			// Verify error response
			var response map[string]interface{}
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to parse JSON response: %v", err)
			}

			expectedError := "Invalid or missing API key"
			if response["error"] != expectedError {
				t.Errorf("Expected error '%s', got '%v'", expectedError, response["error"])
			}
		})
	}
}

// TestServer_Authentication_SecurityVulnerability tests the fixed security bug
func TestServer_Authentication_SecurityVulnerability(t *testing.T) {
	validKeys := []string{"secret-key"}
	server := createTestServerWithAuth(t, true, false, validKeys)

	tests := []struct {
		name     string
		header   string
		expected int
		desc     string
	}{
		{
			name:     "Valid Api-Key",
			header:   "Api-Key secret-key",
			expected: http.StatusOK,
			desc:     "Proper Api-Key format should work",
		},
		{
			name:     "Security Fix - No Space",
			header:   "Api-Keysecret-key",
			expected: http.StatusUnauthorized,
			desc:     "Api-Key without space should be rejected (security fix)",
		},
		{
			name:     "Valid Bearer",
			header:   "Bearer secret-key",
			expected: http.StatusOK,
			desc:     "Proper Bearer format should work",
		},
		{
			name:     "Bearer No Space",
			header:   "Bearersecret-key",
			expected: http.StatusUnauthorized,
			desc:     "Bearer without space should be rejected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/health", nil)
			req.Header.Set("Authorization", tt.header)
			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			if w.Code != tt.expected {
				t.Errorf("%s: Expected status %d, got %d for header '%s'",
					tt.desc, tt.expected, w.Code, tt.header)
			}
		})
	}
}

// TestServer_Authentication_AllEndpointsProtected tests that all endpoints require auth when enabled
func TestServer_Authentication_AllEndpointsProtected(t *testing.T) {
	validKeys := []string{"test-api-key"}
	server := createTestServerWithAuth(t, true, false, validKeys)

	endpoints := []struct {
		method string
		path   string
		body   string
	}{
		{"GET", "/health", ""},
		{"GET", "/ready", ""},
		{"POST", "/api/v1/tailor", `{"baseResume":"test","jobDescription":"test"}`},
		{"POST", "/api/v1/evaluate", `{"baseResume":"test","tailoredResume":"test"}`},
		{"POST", "/api/v1/analyze", `{"jobDescription":"test"}`},
	}

	for _, endpoint := range endpoints {
		t.Run("Unauth_"+endpoint.method+"_"+endpoint.path, func(t *testing.T) {
			var req *http.Request
			if endpoint.body != "" {
				req = httptest.NewRequest(endpoint.method, endpoint.path, bytes.NewBufferString(endpoint.body))
				req.Header.Set("Content-Type", "application/json")
			} else {
				req = httptest.NewRequest(endpoint.method, endpoint.path, nil)
			}

			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			if w.Code != http.StatusUnauthorized {
				t.Errorf("Expected 401 for unauth request to %s %s, got %d",
					endpoint.method, endpoint.path, w.Code)
			}
		})

		t.Run("Auth_"+endpoint.method+"_"+endpoint.path, func(t *testing.T) {
			var req *http.Request
			if endpoint.body != "" {
				req = httptest.NewRequest(endpoint.method, endpoint.path, bytes.NewBufferString(endpoint.body))
				req.Header.Set("Content-Type", "application/json")
			} else {
				req = httptest.NewRequest(endpoint.method, endpoint.path, nil)
			}
			req.Header.Set("Authorization", "Bearer test-api-key")

			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			// Should not be 401 with valid auth
			if w.Code == http.StatusUnauthorized {
				t.Errorf("Got 401 with valid auth for %s %s",
					endpoint.method, endpoint.path)
			}
		})
	}
}

// TestServer_Authentication_MultipleKeys tests support for multiple API keys
func TestServer_Authentication_MultipleKeys(t *testing.T) {
	validKeys := []string{
		"key-1",
		"key-2",
		"very-long-key-with-special-chars-123!@#",
		"sk-rsm-prod-key",
	}
	server := createTestServerWithAuth(t, true, false, validKeys)

	// Test each valid key works
	for i, key := range validKeys {
		t.Run("ValidKey_"+string(rune('A'+i)), func(t *testing.T) {
			req := httptest.NewRequest("GET", "/health", nil)
			req.Header.Set("Authorization", "Bearer "+key)
			w := httptest.NewRecorder()
			server.Router.ServeHTTP(w, req)

			if w.Code != http.StatusOK {
				t.Errorf("Expected 200 for valid key '%s', got %d", key, w.Code)
			}
		})
	}

	// Test invalid key is rejected
	req := httptest.NewRequest("GET", "/health", nil)
	req.Header.Set("Authorization", "Bearer invalid-key")
	w := httptest.NewRecorder()
	server.Router.ServeHTTP(w, req)

	if w.Code != http.StatusUnauthorized {
		t.Errorf("Expected 401 for invalid key, got %d", w.Code)
	}
}

// TestServer_Authentication_LoggingOnly tests logging-only mode behavior
func TestServer_Authentication_LoggingOnly(t *testing.T) {
	validKeys := []string{"test-key"}
	server := createTestServerWithAuth(t, true, true, validKeys)

	// Test with invalid key in logging-only mode
	req := httptest.NewRequest("GET", "/health", nil)
	req.Header.Set("Authorization", "invalid-key")
	w := httptest.NewRecorder()
	server.Router.ServeHTTP(w, req)

	// Current implementation still blocks even in logging-only mode
	// This test documents the current behavior
	if w.Code != http.StatusUnauthorized {
		t.Errorf("Expected 401 even in logging-only mode (current implementation), got %d", w.Code)
	}
}

// Helper functions

// createTestServerWithAuth creates a test server with specific auth configuration
func createTestServerWithAuth(t *testing.T, authEnabled, loggingOnly bool, apiKeys []string) *Server {
	cfg := createAuthTestConfig(authEnabled, loggingOnly, apiKeys)

	svc, err := service.New(service.WithConfig(cfg))
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}

	serverConfig := DefaultConfig()
	serverConfig.Environment = "test"

	return NewServer(serverConfig, cfg, svc)
}

// createAuthTestConfig creates a test configuration with auth settings
func createAuthTestConfig(authEnabled, loggingOnly bool, apiKeys []string) *config.Config {
	return &config.Config{
		AI: config.AIConfig{
			DefaultProvider:    "genai-gemini",
			DefaultModel:       "gemini-2.0-flash-lite",
			DefaultTemperature: 0.7,
			DefaultTimeout:     "30s",
			Operations: map[string]config.OperationAIConfig{
				"tailor": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.7,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled: false, // Disable for tests
					},
				},
				"evaluate": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash-lite",
					Temperature: 0.3,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled: false, // Disable for tests
					},
				},
				"analyze": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.5,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled: false, // Disable for tests
					},
				},
			},
		},
		Providers: config.ProviderConfig{
			GenAI: config.GenAIConfig{},
		},
		Server: config.ServerConfig{
			Port:        "8080",
			Environment: "test",
		},
		Auth: config.AuthConfig{
			Enabled:     authEnabled,
			LoggingOnly: loggingOnly,
			APIKeys:     apiKeys,
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
		},
		Observability: config.ObservabilityConfig{
			TracingEnabled: false,
			ServiceName:    "resumatter-test",
			ServiceVersion: "test",
		},
	}
}

// contains checks if string contains substring
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
