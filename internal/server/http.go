package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sony/gobreaker/v2"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
	"resumatter/pkg/ratelimit"
	"resumatter/pkg/service"
	"resumatter/pkg/types"
)

// Config holds the HTTP server configuration
type HTTPConfig struct {
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
	Environment  string // "development", "production"
}

// DefaultConfig returns default HTTP server configuration
func DefaultConfig() *HTTPConfig {
	return &HTTPConfig{
		Port:         "8080",
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
		Environment:  "development",
	}
}

// Server represents the HTTP server
type Server struct {
	config      *HTTPConfig
	service     service.ServiceInterface
	logger      logger.Logger
	server      *http.Server
	Router      *gin.Engine           // Exported for testing
	Config      *config.Config        // Add reference to main config
	rateLimiter ratelimit.RateLimiter // Rate limiter instance
}

// NewServer creates a new HTTP server instance
func NewServer(httpConfig *HTTPConfig, appConfig *config.Config, svc service.ServiceInterface) *Server {
	if httpConfig == nil {
		httpConfig = DefaultConfig()
	}

	// Set Gin mode based on environment
	if httpConfig.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	server := &Server{
		config:  httpConfig,
		service: svc,
		logger:  svc.Logger().Named("http-server"),
		Config:  appConfig, // Set main config reference
	}

	// Initialize rate limiter if enabled
	if appConfig.RateLimit.Enabled {
		rateLimiter, err := ratelimit.NewFromConfig(appConfig, svc.Logger().Named("rate-limiter"))
		if err != nil {
			svc.Logger().ErrorWithErr(context.Background(), "Failed to create rate limiter", err)
			// Continue without rate limiting rather than failing
		} else {
			server.rateLimiter = rateLimiter
		}
	}

	server.setupRouter()
	server.setupHTTPServer()

	return server
}

// setupRouter configures the Gin router with middleware and routes
func (s *Server) setupRouter() {
	s.Router = gin.New()

	// Add OpenTelemetry middleware for tracing
	s.Router.Use(otelgin.Middleware("resumatter-http"))

	// Add custom middleware
	s.Router.Use(s.loggingMiddleware())
	s.Router.Use(s.recoveryMiddleware())
	s.Router.Use(s.corsMiddleware())

	// Add rate limiting middleware if enabled (before auth to prevent auth bypass attempts)
	if s.rateLimiter != nil {
		rateLimitConfig, err := ratelimit.ResolveConfig(s.Config)
		if err != nil {
			s.logger.ErrorWithErr(context.Background(), "Failed to resolve rate limit config", err)
		} else {
			// Configure middleware to skip health check endpoints
			middlewareConfig := ratelimit.MiddlewareConfig{
				SkipPaths:      []string{"/health", "/ready"},
				IncludeHeaders: ratelimit.DefaultRateLimitHeaders(),
			}
			s.Router.Use(ratelimit.GinMiddlewareWithConfig(s.rateLimiter, rateLimitConfig, s.logger, middlewareConfig))
		}
	}

	// Add API key auth middleware if enabled (after rate limiting)
	if s.Config.Auth.Enabled {
		s.Router.Use(s.apiKeyAuthMiddleware())
	}

	// Health check endpoints
	s.Router.GET("/health", s.healthCheck)
	s.Router.GET("/ready", s.readinessCheck)
	s.Router.GET("/models", s.modelsInfo)

	// API v1 routes
	v1 := s.Router.Group("/api/v1")
	{
		v1.POST("/tailor", s.tailorResume)
		v1.POST("/evaluate", s.evaluateResume)
		v1.POST("/analyze", s.analyzeJob)
		v1.GET("/models", s.modelsInfo)
		v1.GET("/models/:operation", s.modelInfo)
	}
}

// setupHTTPServer configures the underlying HTTP server
func (s *Server) setupHTTPServer() {
	s.server = &http.Server{
		Addr:         ":" + s.config.Port,
		Handler:      s.Router,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
		IdleTimeout:  s.config.IdleTimeout,
	}
}

// Start starts the HTTP server
func (s *Server) Start() error {
	s.logger.Info(context.Background(), "Starting HTTP server",
		logger.String("port", s.config.Port),
		logger.String("environment", s.config.Environment))

	if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start HTTP server: %w", err)
	}

	return nil
}

// Shutdown gracefully shuts down the HTTP server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info(ctx, "Shutting down HTTP server")

	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown HTTP server: %w", err)
	}

	// Close the rate limiter if it exists
	if s.rateLimiter != nil {
		if err := s.rateLimiter.Close(); err != nil {
			s.logger.ErrorWithErr(ctx, "Failed to close rate limiter", err)
			// Continue with shutdown even if rate limiter close fails
		}
	}

	// Close the underlying service
	if err := s.service.Close(); err != nil {
		s.logger.ErrorWithErr(ctx, "Failed to close service", err)
		return fmt.Errorf("failed to close service: %w", err)
	}

	s.logger.Info(ctx, "HTTP server shutdown complete")
	return nil
}

// Middleware implementations

// loggingMiddleware logs HTTP requests with structured logging
func (s *Server) loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Log after processing
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		s.logger.Info(c.Request.Context(), "HTTP request processed",
			logger.String("method", method),
			logger.String("path", path),
			logger.Int("status", statusCode),
			logger.String("client_ip", clientIP),
			logger.String("latency", latency.String()),
			logger.Int("response_size", c.Writer.Size()))
	}
}

// recoveryMiddleware handles panics and returns 500 errors
func (s *Server) recoveryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				s.logger.Error(c.Request.Context(), "Panic recovered in HTTP handler",
					logger.String("error", fmt.Sprintf("%v", err)),
					logger.String("method", c.Request.Method),
					logger.String("path", c.Request.URL.Path))

				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// corsMiddleware handles CORS headers
func (s *Server) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// apiKeyAuthMiddleware checks for a valid API key in the Authorization header
func (s *Server) apiKeyAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg := s.Config
		if len(cfg.Auth.APIKeys) == 0 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "API key authentication is enabled but no keys are configured"})
			return
		}

		header := c.GetHeader("Authorization")
		var key string
		if len(header) > 0 {
			// Support 'Bearer <key>' and 'Api-Key <key>'
			if len(header) > 7 && header[:7] == "Bearer " {
				key = header[7:]
			} else if len(header) > 8 && header[:8] == "Api-Key " {
				key = header[8:]
			} else {
				key = header
			}
			key = strings.TrimSpace(key)
		}

		valid := slices.Contains(cfg.Auth.APIKeys, key)

		if !valid {
			if cfg.Auth.LoggingOnly {
				s.logger.Warn(c.Request.Context(), "API key authentication failed", logger.String("client_ip", c.ClientIP()))
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid or missing API key"})
			return
		}

		c.Next()
	}
}

// Health check endpoints

// healthCheck returns the health status of the service
func (s *Server) healthCheck(c *gin.Context) {
	response := gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"service":   "resumatter",
	}

	// Add basic model availability check
	if models, err := s.service.GetOperationModels(c.Request.Context()); err == nil {
		response["models_available"] = len(models)
		response["operations"] = make([]string, 0, len(models))
		for operation := range models {
			response["operations"] = append(response["operations"].([]string), operation)
		}
	} else {
		s.logger.Warn(c.Request.Context(), "Failed to get model info for health check",
			logger.String("error", err.Error()))
		response["models_available"] = 0
		response["models_error"] = "Failed to retrieve model information"
	}

	c.JSON(http.StatusOK, response)
}

// readinessCheck returns the readiness status of the service
func (s *Server) readinessCheck(c *gin.Context) {
	response := gin.H{
		"status":    "ready",
		"timestamp": time.Now().UTC(),
		"service":   "resumatter",
	}

	// Add model information if available
	if models, err := s.service.GetOperationModels(c.Request.Context()); err == nil {
		response["models"] = models
	} else {
		s.logger.Warn(c.Request.Context(), "Failed to get model info for readiness check",
			logger.String("error", err.Error()))
		response["models_error"] = "Failed to retrieve model information"
	}

	c.JSON(http.StatusOK, response)
}

// modelsInfo returns information about all configured models
func (s *Server) modelsInfo(c *gin.Context) {
	s.logger.Info(c.Request.Context(), "Processing models info request")

	models, err := s.service.GetOperationModels(c.Request.Context())
	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to get models info", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve model information",
			"details": err.Error(),
		})
		return
	}

	response := gin.H{
		"timestamp": time.Now().UTC(),
		"models":    models,
		"count":     len(models),
	}

	s.logger.Info(c.Request.Context(), "Models info request completed successfully",
		logger.Int("model_count", len(models)))

	c.JSON(http.StatusOK, response)
}

// modelInfo returns information about a specific operation's model
func (s *Server) modelInfo(c *gin.Context) {
	operation := c.Param("operation")

	s.logger.Info(c.Request.Context(), "Processing model info request",
		logger.String("operation", operation))

	// Validate operation parameter
	if operation == "" {
		s.logger.Error(c.Request.Context(), "Missing operation parameter")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Operation parameter is required",
		})
		return
	}

	modelInfo, err := s.service.GetModelInfo(c.Request.Context(), operation)
	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to get model info", err,
			logger.String("operation", operation))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve model information",
			"details": err.Error(),
		})
		return
	}

	response := gin.H{
		"timestamp": time.Now().UTC(),
		"operation": operation,
		"model":     modelInfo,
	}

	s.logger.Info(c.Request.Context(), "Model info request completed successfully",
		logger.String("operation", operation),
		logger.String("model_name", modelInfo.Name))

	c.JSON(http.StatusOK, response)
}

// API endpoint handlers

// tailorResume handles POST /api/v1/tailor
func (s *Server) tailorResume(c *gin.Context) {
	var input types.TailorResumeInput

	// Bind JSON input
	if err := c.ShouldBindJSON(&input); err != nil {
		s.logger.Error(c.Request.Context(), "Invalid JSON input for tailor request",
			logger.String("error", err.Error()))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON input",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if input.BaseResume == "" || input.JobDescription == "" {
		s.logger.Error(c.Request.Context(), "Missing required fields for tailor request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Both baseResume and jobDescription are required",
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Processing tailor resume request",
		logger.Int("resume_length", len(input.BaseResume)),
		logger.Int("job_description_length", len(input.JobDescription)))

	// Call service layer
	result, err := s.service.TailorResume(c.Request.Context(),
		service.WithResumeData(input.BaseResume),
		service.WithJobDescriptionData(input.JobDescription))

	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to tailor resume", err)

		// Check if error is from circuit breaker being open
		if isCircuitBreakerOpenError(err) {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"details": "AI service is currently unavailable due to repeated failures",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to tailor resume",
			"details": err.Error(),
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Tailor resume request completed successfully",
		logger.Int("ats_score", result.ATSAnalysis.Score))

	c.JSON(http.StatusOK, result)
}

// evaluateResume handles POST /api/v1/evaluate
func (s *Server) evaluateResume(c *gin.Context) {
	var input types.EvaluateResumeInput

	// Bind JSON input
	if err := c.ShouldBindJSON(&input); err != nil {
		s.logger.Error(c.Request.Context(), "Invalid JSON input for evaluate request",
			logger.String("error", err.Error()))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON input",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if input.BaseResume == "" || input.TailoredResume == "" {
		s.logger.Error(c.Request.Context(), "Missing required fields for evaluate request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Both baseResume and tailoredResume are required",
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Processing evaluate resume request",
		logger.Int("base_resume_length", len(input.BaseResume)),
		logger.Int("tailored_resume_length", len(input.TailoredResume)))

	// Call service layer
	result, err := s.service.EvaluateResume(c.Request.Context(),
		service.WithBaseResumeData(input.BaseResume),
		service.WithTailoredResumeData(input.TailoredResume))

	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to evaluate resume", err)

		// Check if error is from circuit breaker being open
		if isCircuitBreakerOpenError(err) {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"details": "AI service is currently unavailable due to repeated failures",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to evaluate resume",
			"details": err.Error(),
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Evaluate resume request completed successfully",
		logger.Int("findings_count", len(result.Findings)))

	c.JSON(http.StatusOK, result)
}

// analyzeJob handles POST /api/v1/analyze
func (s *Server) analyzeJob(c *gin.Context) {
	var input types.AnalyzeJobInput

	// Bind JSON input
	if err := c.ShouldBindJSON(&input); err != nil {
		s.logger.Error(c.Request.Context(), "Invalid JSON input for analyze request",
			logger.String("error", err.Error()))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON input",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if input.JobDescription == "" {
		s.logger.Error(c.Request.Context(), "Missing required field for analyze request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "jobDescription is required",
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Processing analyze job request",
		logger.Int("job_description_length", len(input.JobDescription)))

	// Call service layer
	result, err := s.service.AnalyzeJob(c.Request.Context(),
		service.WithJobDescriptionDataForAnalysis(input.JobDescription))

	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to analyze job", err)

		// Check if error is from circuit breaker being open
		if isCircuitBreakerOpenError(err) {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"details": "AI service is currently unavailable due to repeated failures",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to analyze job",
			"details": err.Error(),
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Analyze job request completed successfully",
		logger.Int("job_quality_score", result.JobQualityScore))

	c.JSON(http.StatusOK, result)
}

// isCircuitBreakerOpenError checks if the error is from a circuit breaker being open
// Flow: gobreaker.CircuitBreaker.Execute() -> AICircuitBreaker.Execute() ->
//
//	client.generateJSON() -> service layer -> HTTP handler
//
// The gobreaker library returns ErrOpenState directly when circuit is open,
// and errors.Is() can detect it even when wrapped in multiple error layers
func isCircuitBreakerOpenError(err error) bool {
	return errors.Is(err, gobreaker.ErrOpenState)
}
