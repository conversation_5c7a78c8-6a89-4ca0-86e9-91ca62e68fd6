//go:build fast

package server

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/service"
	"resumatter/pkg/types"
)

func TestServer_HealthCheck(t *testing.T) {
	// Create test server
	server := createTestServer(t)

	// Test health endpoint
	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	server.Router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	if response["status"] != "healthy" {
		t.Errorf("Expected status 'healthy', got %v", response["status"])
	}

	if response["service"] != "resumatter" {
		t.Errorf("Expected service 'resumatter', got %v", response["service"])
	}
}

func TestServer_ReadinessCheck(t *testing.T) {
	// Create test server
	server := createTestServer(t)

	// Test readiness endpoint
	req := httptest.NewRequest("GET", "/ready", nil)
	w := httptest.NewRecorder()
	server.Router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	if response["status"] != "ready" {
		t.Errorf("Expected status 'ready', got %v", response["status"])
	}
}

func TestServer_TailorResume_InvalidInput(t *testing.T) {
	server := createTestServer(t)

	tests := []struct {
		name           string
		input          types.TailorResumeInput
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "empty input",
			input:          types.TailorResumeInput{},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Both baseResume and jobDescription are required",
		},
		{
			name: "missing job description",
			input: types.TailorResumeInput{
				BaseResume: "Some resume content",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Both baseResume and jobDescription are required",
		},
		{
			name: "missing base resume",
			input: types.TailorResumeInput{
				JobDescription: "Some job description",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Both baseResume and jobDescription are required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.input)
			req := httptest.NewRequest("POST", "/api/v1/tailor", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			server.Router.ServeHTTP(w, req)

			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			var response map[string]interface{}
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to parse JSON response: %v", err)
			}

			if response["error"] != tt.expectedError {
				t.Errorf("Expected error '%s', got '%v'", tt.expectedError, response["error"])
			}
		})
	}
}

func TestServer_EvaluateResume_InvalidInput(t *testing.T) {
	server := createTestServer(t)

	tests := []struct {
		name           string
		input          types.EvaluateResumeInput
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "empty input",
			input:          types.EvaluateResumeInput{},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Both baseResume and tailoredResume are required",
		},
		{
			name: "missing tailored resume",
			input: types.EvaluateResumeInput{
				BaseResume: "Some resume content",
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Both baseResume and tailoredResume are required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, _ := json.Marshal(tt.input)
			req := httptest.NewRequest("POST", "/api/v1/evaluate", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			server.Router.ServeHTTP(w, req)

			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			var response map[string]interface{}
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to parse JSON response: %v", err)
			}

			if response["error"] != tt.expectedError {
				t.Errorf("Expected error '%s', got '%v'", tt.expectedError, response["error"])
			}
		})
	}
}

func TestServer_AnalyzeJob_InvalidInput(t *testing.T) {
	server := createTestServer(t)

	input := types.AnalyzeJobInput{}
	jsonData, _ := json.Marshal(input)
	req := httptest.NewRequest("POST", "/api/v1/analyze", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	server.Router.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status 400, got %d", w.Code)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	expectedError := "jobDescription is required"
	if response["error"] != expectedError {
		t.Errorf("Expected error '%s', got '%v'", expectedError, response["error"])
	}
}

func TestServer_InvalidJSON(t *testing.T) {
	server := createTestServer(t)

	req := httptest.NewRequest("POST", "/api/v1/tailor", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	server.Router.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status 400, got %d", w.Code)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	if response["error"] != "Invalid JSON input" {
		t.Errorf("Expected 'Invalid JSON input' error, got '%v'", response["error"])
	}
}

func TestServer_CORS_Headers(t *testing.T) {
	server := createTestServer(t)

	req := httptest.NewRequest("OPTIONS", "/api/v1/tailor", nil)
	w := httptest.NewRecorder()

	server.Router.ServeHTTP(w, req)

	if w.Code != http.StatusNoContent {
		t.Errorf("Expected status 204 for OPTIONS request, got %d", w.Code)
	}

	expectedHeaders := map[string]string{
		"Access-Control-Allow-Origin":  "*",
		"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
		"Access-Control-Allow-Headers": "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization",
	}

	for header, expectedValue := range expectedHeaders {
		if w.Header().Get(header) != expectedValue {
			t.Errorf("Expected header %s: %s, got: %s", header, expectedValue, w.Header().Get(header))
		}
	}
}

// createTestServer creates a test server instance for testing
func createTestServer(t *testing.T) *Server {
	cfg := &config.Config{
		AI: config.AIConfig{
			DefaultProvider:    "genai-gemini",
			DefaultModel:       "gemini-2.0-flash-lite",
			DefaultTemperature: 0.7,
			DefaultTimeout:     "30s",
			Operations: map[string]config.OperationAIConfig{
				"tailor": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.7,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disable for tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
				"evaluate": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash-lite",
					Temperature: 0.3,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disable for tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
				"analyze": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.5,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disable for tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
			},
		},
		Providers: config.ProviderConfig{
			GenAI: config.GenAIConfig{},
		},
		Server: config.ServerConfig{
			Port:        "8080",
			Environment: "test",
		},
		Auth: config.AuthConfig{
			Enabled:     false,
			LoggingOnly: true,
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
		},
		Observability: config.ObservabilityConfig{
			TracingEnabled: false,
			ServiceName:    "resumatter-test",
			ServiceVersion: "test",
		},
	}

	svc, err := service.New(service.WithConfig(cfg))
	if err != nil {
		t.Fatalf("Failed to create service: %v", err)
	}

	serverConfig := DefaultConfig()
	serverConfig.Environment = "test"

	return NewServer(serverConfig, cfg, svc)
}
