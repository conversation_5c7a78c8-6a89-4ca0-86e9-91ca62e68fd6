package cli

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"

	"resumatter/internal/server"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
	"resumatter/pkg/observability"
	"resumatter/pkg/service"
)

// ServerApp represents the server CLI application
type ServerApp struct {
	rootCmd *cobra.Command
}

// NewServerApp creates a new server CLI application
func NewServerApp() (*ServerApp, error) {
	app := &ServerApp{}
	app.setupCommands()
	return app, nil
}

// Execute runs the CLI application
func (app *ServerApp) Execute() error {
	return app.rootCmd.Execute()
}

// setupCommands configures all CLI commands
func (app *ServerApp) setupCommands() {
	app.rootCmd = &cobra.Command{
		Use:   "resumatter",
		Short: "Resumatter HTTP API Server",
		Long: `Resumatter Server is the HTTP API backend for the AI-powered resume optimization service.

It provides REST endpoints for:
- Resume tailoring for specific job descriptions
- Resume evaluation for accuracy and consistency  
- Job description analysis for quality and effectiveness

The server is designed for enterprise deployment with support for authentication,
rate limiting, circuit breakers, and comprehensive observability.`,
		SilenceUsage: true,
	}

	// Add version command
	app.rootCmd.AddCommand(&cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println("resumatter version dev") // TODO: Add proper versioning
		},
	})

	// Add serve command
	app.rootCmd.AddCommand(app.createServeCommand())
}

// createServeCommand creates the serve command
func (app *ServerApp) createServeCommand() *cobra.Command {
	var (
		port        string
		environment string
		configFile  string
	)

	cmd := &cobra.Command{
		Use:   "serve",
		Short: "Start the HTTP API server",
		Long: `Start the Resumatter HTTP API server.

The server will start on the specified port and serve the REST API endpoints.
Configuration can be provided via environment variables, command-line flags,
or a configuration file.

Examples:
  resumatter serve                           # Start with defaults
  resumatter serve --port 8080               # Start on port 8080
  resumatter serve --environment production  # Start in production mode
  resumatter serve --config /etc/resumatter/server.yaml`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return app.runServe(port, environment, configFile)
		},
	}

	// Add flags
	cmd.Flags().StringVarP(&port, "port", "p", "8080", "Port to listen on")
	cmd.Flags().StringVarP(&environment, "environment", "e", "development", "Environment (development, production)")
	cmd.Flags().StringVarP(&configFile, "config", "c", "", "Configuration file path")

	return cmd
}

// runServe implements the serve command logic
func (app *ServerApp) runServe(port, environment, configFile string) error {
	// Load configuration using Viper
	provider := config.NewProvider()

	// If config file is specified, try to use it
	if configFile != "" {
		// TODO: Add support for custom config file path in Viper
		fmt.Printf("Using config file: %s\n", configFile)
	}

	// Load configuration
	cfg, err := provider.Load()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}
	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("configuration error: %w", err)
	}

	fmt.Printf("Configuration loaded successfully (auth enabled: %v)\n", cfg.Auth.Enabled)

	// Create logger
	log := logger.NewDefault().Named("resumatter")

	// Create observability provider
	obsConfig := observability.LoadConfigFromEnv()
	obsConfig.ServiceName = "resumatter"
	obsConfig.MetricsEnabled = true
	obsConfig.MetricsExporter = observability.MetricsExporterJSONFile
	obsConfig.MetricsFilePath = "resumatter-metrics.json"

	obsProvider, err := observability.NewProvider(obsConfig)
	if err != nil {
		return fmt.Errorf("failed to create observability provider: %w", err)
	}
	defer func() {
		if err := obsProvider.Shutdown(context.Background()); err != nil {
			log.ErrorWithErr(context.Background(), "Failed to shutdown observability provider", err)
		}
	}()

	// Create service
	svc, err := service.New(
		service.WithConfig(cfg),
		service.WithLogger(log),
		service.WithObservability(obsProvider),
	)
	if err != nil {
		return fmt.Errorf("failed to create service: %w", err)
	}
	defer svc.Close()

	// Create HTTP server configuration
	serverConfig := server.DefaultConfig()

	// Override with command-line flags
	if port != "" {
		serverConfig.Port = port
	}
	if environment != "" {
		serverConfig.Environment = environment
	}

	// Override with environment variables (for container deployment)
	if envPort := os.Getenv("PORT"); envPort != "" {
		serverConfig.Port = envPort
	}
	if envEnv := os.Getenv("ENVIRONMENT"); envEnv != "" {
		serverConfig.Environment = envEnv
	}

	httpServer := server.NewServer(serverConfig, cfg, svc)

	// Start server in a goroutine
	go func() {
		log.Info(context.Background(), "Starting Resumatter HTTP server",
			logger.String("port", serverConfig.Port),
			logger.String("environment", serverConfig.Environment))

		if err := httpServer.Start(); err != nil {
			log.ErrorWithErr(context.Background(), "HTTP server failed", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info(context.Background(), "Shutting down server...")

	// Give the server 30 seconds to shutdown gracefully
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := httpServer.Shutdown(ctx); err != nil {
		return fmt.Errorf("server forced to shutdown: %w", err)
	}

	log.Info(context.Background(), "Server exited")
	return nil
}
